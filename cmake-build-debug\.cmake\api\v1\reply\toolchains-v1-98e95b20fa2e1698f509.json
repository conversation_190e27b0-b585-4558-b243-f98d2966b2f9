{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include", "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/include", "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed", "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/x86_64-w64-mingw32/include"], "linkDirectories": [], "linkFrameworkDirectories": [], "linkLibraries": []}, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/gcc.exe", "version": "13.1.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++", "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32", "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward", "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include", "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/include", "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed", "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/x86_64-w64-mingw32/include"], "linkDirectories": [], "linkFrameworkDirectories": [], "linkLibraries": []}, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/g++.exe", "version": "13.1.0"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}, {"compiler": {"implicit": {}, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/mingw/bin/windres.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}