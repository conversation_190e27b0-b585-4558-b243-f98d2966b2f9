/**
 * @file SH5001_Sensor.h
 * @brief SH5001 IMU传感器驱动适配器头文件
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 将现有的SH5001驱动适配到新的抽象接口
 * 确保向后兼容性和统一的API接口
 */

#ifndef SH5001_SENSOR_H
#define SH5001_SENSOR_H

#include "IMU_Sensor_Interface.h"
#include <Wire.h>

// SH5001寄存器地址定义
#define SH5001_ADDR             0x37    // SH5001 I2C地址 (SDO=VDD)
#define SH5001_ADDR_ALT         0x36    // SH5001 I2C地址 (SDO=GND)
#define SH5001_CHIP_ID          0x1F    // 设备ID寄存器
#define SH5001_EXPECTED_ID      0xA1    // 期望的设备ID

// 数据寄存器
#define SH5001_ACC_XL           0x00    // 加速度计X轴低字节
#define SH5001_ACC_XH           0x01    // 加速度计X轴高字节
#define SH5001_ACC_YL           0x02    // 加速度计Y轴低字节
#define SH5001_ACC_YH           0x03    // 加速度计Y轴高字节
#define SH5001_ACC_ZL           0x04    // 加速度计Z轴低字节
#define SH5001_ACC_ZH           0x05    // 加速度计Z轴高字节
#define SH5001_GYRO_XL          0x06    // 陀螺仪X轴低字节
#define SH5001_GYRO_XH          0x07    // 陀螺仪X轴高字节
#define SH5001_GYRO_YL          0x08    // 陀螺仪Y轴低字节
#define SH5001_GYRO_YH          0x09    // 陀螺仪Y轴高字节
#define SH5001_GYRO_ZL          0x0A    // 陀螺仪Z轴低字节
#define SH5001_GYRO_ZH          0x0B    // 陀螺仪Z轴高字节
#define SH5001_TEMP_ZL          0x0C    // 温度低字节
#define SH5001_TEMP_ZH          0x0D    // 温度高字节

// 配置寄存器
#define SH5001_ACC_CONF0        0x20    // 加速度计配置寄存器0
#define SH5001_ACC_CONF1        0x21    // 加速度计配置寄存器1
#define SH5001_ACC_CONF2        0x22    // 加速度计配置寄存器2
#define SH5001_GYRO_CONF0       0x23    // 陀螺仪配置寄存器0
#define SH5001_GYRO_CONF1       0x24    // 陀螺仪配置寄存器1
#define SH5001_GYRO_CONF2       0x25    // 陀螺仪配置寄存器2
#define SH5001_TEMP_CONF0       0x28    // 温度配置寄存器0
#define SH5001_POWER_MODE       0x30    // 电源模式寄存器

// 量程配置值
#define SH5001_ACCEL_RANGE_4G   0x10    // ±4g
#define SH5001_ACCEL_RANGE_8G   0x20    // ±8g
#define SH5001_ACCEL_RANGE_16G  0x30    // ±16g

#define SH5001_GYRO_RANGE_250   0x30    // ±250°/s
#define SH5001_GYRO_RANGE_500   0x40    // ±500°/s
#define SH5001_GYRO_RANGE_1000  0x50    // ±1000°/s
#define SH5001_GYRO_RANGE_2000  0x60    // ±2000°/s

// 输出数据率配置
#define SH5001_ODR_125HZ        0x03    // 125Hz
#define SH5001_ODR_250HZ        0x04    // 250Hz
#define SH5001_ODR_500HZ        0x05    // 500Hz
#define SH5001_ODR_1000HZ       0x06    // 1000Hz

/**
 * @brief SH5001 IMU传感器驱动类
 * 
 * 实现IMU_Sensor_Interface接口，提供SH5001传感器的具体实现
 * 支持加速度计、陀螺仪和温度传感器数据读取
 */
class SH5001_Sensor : public IMU_Sensor_Interface {
private:
    uint8_t device_address;         ///< I2C设备地址
    float gyro_scale;               ///< 陀螺仪量程转换因子
    float accel_scale;              ///< 加速度计量程转换因子
    bool connection_verified;       ///< 连接验证标志
    
    // 校准偏移量
    float accel_offset[3];          ///< 加速度计偏移量
    float gyro_offset[3];           ///< 陀螺仪偏移量
    
    // 私有方法
    bool writeRegister(uint8_t reg, uint8_t value);
    uint8_t readRegister(uint8_t reg);
    bool readRegisters(uint8_t reg, uint8_t* buffer, uint8_t length);
    int16_t combineBytes(uint8_t high, uint8_t low);
    
    // SH5001特有的配置方法
    IMU_Status softReset();
    IMU_Status configureAccelerometer();
    IMU_Status configureGyroscope();
    IMU_Status configureTemperature();
    IMU_Status setAccelRange(uint16_t range);
    IMU_Status setGyroRange(uint16_t range);
    IMU_Status setSampleRate(uint16_t rate);
    
    // 量程转换方法
    void updateScaleFactors();
    uint8_t mapAccelRange(uint16_t range);
    uint8_t mapGyroRange(uint16_t range);
    uint8_t mapSampleRate(uint16_t rate);

public:
    /**
     * @brief 构造函数
     * @param addr I2C设备地址 (默认0x37)
     */
    SH5001_Sensor(uint8_t addr = SH5001_ADDR);
    
    /**
     * @brief 析构函数
     */
    virtual ~SH5001_Sensor();

    // 实现IMU_Sensor_Interface接口
    virtual IMU_Status begin(const IMU_Config& config = {}) override;
    virtual IMU_Status readData(IMU_Data& data) override;
    virtual IMU_Status readRawData(IMU_RawData& raw_data) override;
    virtual bool isConnected() override;
    virtual IMU_Type getType() const override;
    virtual uint8_t getDeviceID() override;
    virtual IMU_Status reset() override;
    virtual IMU_Status calibrate(uint32_t calibration_time = 5000) override;
    virtual IMU_Status setConfig(const IMU_Config& config) override;
    virtual IMU_Status getConfig(IMU_Config& config) override;
    virtual const char* getLastError() override;
    virtual const char* getInfo() override;

    /**
     * @brief 设置I2C地址
     * @param addr 新的I2C地址
     * @return IMU_Status 设置状态
     */
    IMU_Status setI2CAddress(uint8_t addr);

    /**
     * @brief 获取温度数据
     * @return float 温度值 (摄氏度)
     */
    float getTemperature();

    /**
     * @brief 执行自检
     * @return IMU_Status 自检状态
     */
    IMU_Status selfTest();

    /**
     * @brief 获取校准偏移量
     * @param accel_offsets 加速度计偏移量 [x, y, z]
     * @param gyro_offsets 陀螺仪偏移量 [x, y, z]
     */
    void getCalibrationOffsets(float accel_offsets[3], float gyro_offsets[3]);

    /**
     * @brief 设置校准偏移量
     * @param accel_offsets 加速度计偏移量 [x, y, z]
     * @param gyro_offsets 陀螺仪偏移量 [x, y, z]
     * @return IMU_Status 设置状态
     */
    IMU_Status setCalibrationOffsets(const float accel_offsets[3], const float gyro_offsets[3]);
};

#endif // SH5001_SENSOR_H
