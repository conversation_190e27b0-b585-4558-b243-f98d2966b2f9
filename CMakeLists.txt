cmake_minimum_required(VERSION 3.28)
project(Arduino_ESP32C3_IMU)

set(CMAKE_CXX_STANDARD 14)

include_directories(ESP32C3_IMU_Fusion)
include_directories(libraries/Fusion/src)

add_executable(Arduino_ESP32C3_IMU
        ESP32C3_IMU_Fusion/IMU_Driver.cpp
        ESP32C3_IMU_Fusion/IMU_Driver.h
        ESP32C3_IMU_Fusion/main.c
        libraries/Fusion/src/Fusion.h
        libraries/Fusion/src/FusionAhrs.c
        libraries/Fusion/src/FusionAhrs.h
        libraries/Fusion/src/FusionAxes.h
        libraries/Fusion/src/FusionCalibration.h
        libraries/Fusion/src/FusionCompass.c
        libraries/Fusion/src/FusionCompass.h
        libraries/Fusion/src/FusionConvention.h
        libraries/Fusion/src/FusionMath.h
        libraries/Fusion/src/FusionOffset.c
        libraries/Fusion/src/FusionOffset.h
        ESP32C3_IMU_Fusion/ESP32C3_IMU_Fusion.ino)
