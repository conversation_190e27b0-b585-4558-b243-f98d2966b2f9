/**
 * @file IMU_Driver.cpp
 * @brief IMU传感器驱动实现 (Arduino版本) - SH5001
 * <AUTHOR> IMU Fusion Project
 */

#include "IMU_Driver.h"

/**
 * @brief 构造函数
 */
IMU_Driver::IMU_Driver(uint8_t addr) {
    deviceAddress = addr;
    gyroScale = 16.4f;     // ±2000°/s时的LSB/(°/s) (32768/2000)
    accelScale = 2048.0f;  // ±16g时的LSB/g (32768/16)
}

/**
 * @brief 初始化IMU传感器
 */
bool IMU_Driver::begin() {
    // 检查设备连接
    if (!testConnection()) {
        Serial.println("错误: 无法检测到SH5001设备");
        return false;
    }

    Serial.print("检测到SH5001设备，ID: 0x37");
    Serial.println(getDeviceID(), HEX);

    // 软复位
    softReset();
    delay(100); // 等待复位完成

    // 配置加速度计
    configAccelerometer();

    // 配置陀螺仪
    configGyroscope();

    // 配置温度传感器
    configTemperature();

    delay(50); // 等待配置生效

    Serial.println("SH5001初始化完成");
    return true;
}

/**
 * @brief 读取IMU数据
 */
bool IMU_Driver::readData(float& accel_x, float& accel_y, float& accel_z,
                         float& gyro_x, float& gyro_y, float& gyro_z) {
    int16_t accel_raw[3];
    int16_t gyro_raw[3];
    
    if (!readRawData(accel_raw, gyro_raw)) {
        return false;
    }
    
    // 转换为物理单位
    accel_x = accel_raw[0] / accelScale;
    accel_y = accel_raw[1] / accelScale;
    accel_z = accel_raw[2] / accelScale;
    
    gyro_x = gyro_raw[0] / gyroScale;
    gyro_y = gyro_raw[1] / gyroScale;
    gyro_z = gyro_raw[2] / gyroScale;
    
    return true;
}

/**
 * @brief 读取原始数据
 */
bool IMU_Driver::readRawData(int16_t accel_raw[3], int16_t gyro_raw[3]) {
    uint8_t buffer[12];

    // 从SH5001_ACC_XL开始连续读取12个字节
    // 0x00-0x05: 加速度计数据 (X, Y, Z) - 小端格式
    // 0x06-0x0B: 陀螺仪数据 (X, Y, Z) - 小端格式
    if (!readRegisters(SH5001_ACC_XL, buffer, 12)) {
        return false;
    }

    // 组合高低字节 (小端格式: 低字节在前，高字节在后)
    accel_raw[0] = (int16_t)((buffer[1] << 8) | buffer[0]);   // ACCEL_X
    accel_raw[1] = (int16_t)((buffer[3] << 8) | buffer[2]);   // ACCEL_Y
    accel_raw[2] = (int16_t)((buffer[5] << 8) | buffer[4]);   // ACCEL_Z

    gyro_raw[0] = (int16_t)((buffer[7] << 8) | buffer[6]);    // GYRO_X
    gyro_raw[1] = (int16_t)((buffer[9] << 8) | buffer[8]);    // GYRO_Y
    gyro_raw[2] = (int16_t)((buffer[11] << 8) | buffer[10]);  // GYRO_Z

    return true;
}

/**
 * @brief 检查设备连接
 */
bool IMU_Driver::testConnection() {
    uint8_t deviceID = getDeviceID();
    return (deviceID == 0xA1);
}

/**
 * @brief 设置陀螺仪量程
 */
bool IMU_Driver::setGyroRange(uint8_t range) {
    uint8_t reg_data;

    // 读取当前配置
    reg_data = readRegister(SH5001_GYRO_CONF1);

    // 清除量程位并设置新量程
    reg_data = (reg_data & 0x8F) | range;

    if (!writeRegister(SH5001_GYRO_CONF1, reg_data)) {
        return false;
    }

    // 更新量程转换因子
    switch (range) {
        case GYRO_RANGE_250DPS:
            gyroScale = 131.0f;     // 32768/250
            break;
        case GYRO_RANGE_500DPS:
            gyroScale = 65.5f;      // 32768/500
            break;
        case GYRO_RANGE_1000DPS:
            gyroScale = 32.8f;      // 32768/1000
            break;
        case GYRO_RANGE_2000DPS:
            gyroScale = 16.4f;      // 32768/2000
            break;
        default:
            return false;
    }

    return true;
}

/**
 * @brief 设置加速度计量程
 */
bool IMU_Driver::setAccelRange(uint8_t range) {
    uint8_t reg_data;

    // 读取当前配置
    reg_data = readRegister(SH5001_ACC_CONF1);

    // 清除量程位并设置新量程
    reg_data = (reg_data & 0xCF) | range;

    if (!writeRegister(SH5001_ACC_CONF1, reg_data)) {
        return false;
    }

    // 更新量程转换因子
    switch (range) {
        case ACCEL_RANGE_4G:
            accelScale = 8192.0f;   // 32768/4
            break;
        case ACCEL_RANGE_8G:
            accelScale = 4096.0f;   // 32768/8
            break;
        case ACCEL_RANGE_16G:
            accelScale = 2048.0f;   // 32768/16
            break;
        default:
            return false;
    }

    return true;
}

/**
 * @brief 获取设备ID
 */
uint8_t IMU_Driver::getDeviceID() {
    return readRegister(SH5001_CHIP_ID);
}

/**
 * @brief 写寄存器
 */
bool IMU_Driver::writeRegister(uint8_t reg, uint8_t value) {
    Wire.beginTransmission(deviceAddress);
    Wire.write(reg);
    Wire.write(value);
    return (Wire.endTransmission() == 0);
}

/**
 * @brief 读单个寄存器
 */
uint8_t IMU_Driver::readRegister(uint8_t reg) {
    Wire.beginTransmission(deviceAddress);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) {
        return 0;
    }
    
    Wire.requestFrom(deviceAddress, (uint8_t)1);
    if (Wire.available()) {
        return Wire.read();
    }
    return 0;
}

/**
 * @brief 读多个寄存器
 */
bool IMU_Driver::readRegisters(uint8_t reg, uint8_t* buffer, uint8_t length) {
    Wire.beginTransmission(deviceAddress);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) {
        return false;
    }
    
    Wire.requestFrom(deviceAddress, length);
    uint8_t i = 0;
    while (Wire.available() && i < length) {
        buffer[i++] = Wire.read();
    }
    
    return (i == length);
}

/**
 * @brief 组合高低字节
 */
int16_t IMU_Driver::combineBytes(uint8_t high, uint8_t low) {
    return (int16_t)((high << 8) | low);
}

/**
 * @brief 软复位
 */
void IMU_Driver::softReset() {
    writeRegister(0x2B, 0x01);
    writeRegister(0x00, 0x73);
    delay(50);
}

/**
 * @brief 配置加速度计
 */
void IMU_Driver::configAccelerometer() {
    uint8_t reg_data;

    // 配置ACC_CONF0: 启用滤波器和旁路
    reg_data = readRegister(SH5001_ACC_CONF0);
    reg_data = (reg_data & 0xFC) | SH5001_ACC_FILTER_EN | SH5001_ACC_BYPASS_EN;
    writeRegister(SH5001_ACC_CONF0, reg_data);

    // 配置ACC_CONF1: ODR和量程
    reg_data = readRegister(SH5001_ACC_CONF1);
    reg_data = (reg_data & 0x80) | SH5001_ACC_ODR_125HZ | SH5001_ACC_RANGE_16G;
    writeRegister(SH5001_ACC_CONF1, reg_data);

    // 配置ACC_CONF2: 截止频率
    reg_data = readRegister(SH5001_ACC_CONF2);
    reg_data = (reg_data & 0xF0) | SH5001_ACC_ODRX040;
    writeRegister(SH5001_ACC_CONF2, reg_data);
}

/**
 * @brief 配置陀螺仪
 */
void IMU_Driver::configGyroscope() {
    uint8_t reg_data;

    // 配置GYRO_CONF0: 启用滤波器和旁路
    reg_data = readRegister(SH5001_GYRO_CONF0);
    reg_data = (reg_data & 0x7C) | SH5001_GYRO_FILTER_EN | SH5001_GYRO_BYPASS_EN;
    writeRegister(SH5001_GYRO_CONF0, reg_data);

    // 配置GYRO_CONF1: ODR和量程
    reg_data = readRegister(SH5001_GYRO_CONF1);
    reg_data = (reg_data & 0x80) | SH5001_GYRO_ODR_125HZ | SH5001_GYRO_RANGE_2000;
    writeRegister(SH5001_GYRO_CONF1, reg_data);

    // 配置GYRO_CONF2: 截止频率
    reg_data = readRegister(SH5001_GYRO_CONF2);
    reg_data = (reg_data & 0xF0) | SH5001_GYRO_ODRX040;
    writeRegister(SH5001_GYRO_CONF2, reg_data);
}

/**
 * @brief 配置温度传感器
 */
void IMU_Driver::configTemperature() {
    uint8_t reg_data;

    reg_data = readRegister(SH5001_TEMP_CONF0);
    reg_data = (reg_data & 0xF8) | SH5001_TEMP_ODR_63HZ | SH5001_TEMP_EN;
    writeRegister(SH5001_TEMP_CONF0, reg_data);
}

/**
 * @brief 读取温度数据
 */
float IMU_Driver::getTemperature() {
    uint8_t temp_data[2];
    uint16_t temp_ref[2];

    // 读取温度参考值
    if (readRegisters(0x29, temp_data, 2)) { // TEMP_CONF1, TEMP_CONF2
        temp_ref[0] = ((uint16_t)(temp_data[1] & 0x0F) << 8) | temp_data[0];
    }

    // 读取温度数据
    if (readRegisters(SH5001_TEMP_ZL, temp_data, 2)) {
        temp_ref[1] = ((uint16_t)(temp_data[1] & 0x0F) << 8) | temp_data[0];

        // 计算温度 (根据SH5001数据手册公式)
        return (((float)(temp_ref[1] - temp_ref[0])) / 14.0) + 25.0;
    }

    return 0.0;
}
