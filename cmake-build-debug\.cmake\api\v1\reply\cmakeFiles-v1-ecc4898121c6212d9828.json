{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.28.6/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.28.6/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.28.6/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/3.28.6/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-C-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-CXX-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}], "kind": "cmakeFiles", "paths": {"build": "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/cmake-build-debug", "source": "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU"}, "version": {"major": 1, "minor": 0}}