/**
 * @file IMU_Fusion_Engine.h
 * @brief IMU数据融合引擎头文件
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 封装Fusion库的AHRS算法，提供简单易用的API接口
 * 支持姿态估计、传感器数据融合等功能
 */

#ifndef IMU_FUSION_ENGINE_H
#define IMU_FUSION_ENGINE_H

#include <Arduino.h>
#include "Fusion.h"
#include "IMU_Sensor_Interface.h"

/**
 * @brief 姿态数据结构
 */
struct Attitude_Data {
    float roll;         ///< 横滚角 (度)
    float pitch;        ///< 俯仰角 (度)
    float yaw;          ///< 偏航角 (度)
    float heading;      ///< 航向角 (度)
};

/**
 * @brief 四元数数据结构
 */
struct Quaternion_Data {
    float w;            ///< 四元数实部
    float x;            ///< 四元数虚部i
    float y;            ///< 四元数虚部j
    float z;            ///< 四元数虚部k
};

/**
 * @brief 融合算法配置结构
 */
struct Fusion_Config {
    FusionConvention convention;        ///< 坐标系约定 (默认: NWU)
    float gain;                         ///< 融合增益 (默认: 0.5)
    float gyroscope_range;              ///< 陀螺仪量程 (度/秒)
    float acceleration_rejection;       ///< 加速度拒绝阈值 (度)
    float magnetic_rejection;           ///< 磁场拒绝阈值 (度)
    uint32_t recovery_trigger_period;   ///< 恢复触发周期 (采样数)
    float sample_period;                ///< 采样周期 (秒)
    bool use_magnetometer;              ///< 是否使用磁力计
};

/**
 * @brief 融合算法状态枚举
 */
enum class Fusion_Status {
    OK = 0,                 ///< 正常
    ERROR_INIT,             ///< 初始化错误
    ERROR_INVALID_DATA,     ///< 无效数据
    ERROR_SENSOR_FAILURE,   ///< 传感器故障
    WARNING_HIGH_ACCEL,     ///< 高加速度警告
    WARNING_HIGH_GYRO       ///< 高角速度警告
};

/**
 * @brief IMU数据融合引擎类
 * 
 * 封装Fusion库的AHRS算法，提供简单易用的融合接口
 * 支持姿态估计、四元数输出、欧拉角输出等功能
 */
class IMU_Fusion_Engine {
private:
    FusionAhrs ahrs;                    ///< Fusion AHRS算法实例
    Fusion_Config config;               ///< 融合算法配置
    Fusion_Status status;               ///< 当前状态
    
    bool initialized;                   ///< 初始化标志
    uint32_t sample_count;              ///< 采样计数
    uint32_t last_update_time;          ///< 上次更新时间
    
    // 数据缓存
    Attitude_Data attitude;             ///< 姿态数据
    Quaternion_Data quaternion;         ///< 四元数数据
    
    // 统计信息
    float max_accel_magnitude;          ///< 最大加速度幅值
    float max_gyro_magnitude;           ///< 最大角速度幅值
    
    char error_message[128];            ///< 错误信息缓冲区

    // 私有方法
    void updateStatistics(const IMU_Data& data);
    bool validateData(const IMU_Data& data);
    void updateQuaternion();
    void updateAttitude();

public:
    /**
     * @brief 构造函数
     */
    IMU_Fusion_Engine();

    /**
     * @brief 析构函数
     */
    ~IMU_Fusion_Engine();

    /**
     * @brief 初始化融合引擎
     * @param config 融合算法配置
     * @return Fusion_Status 初始化状态
     */
    Fusion_Status begin(const Fusion_Config& config = {});

    /**
     * @brief 更新IMU数据
     * @param data IMU传感器数据
     * @return Fusion_Status 更新状态
     */
    Fusion_Status update(const IMU_Data& data);

    /**
     * @brief 更新IMU数据 (分离接口)
     * @param accel_x 加速度计X轴 (g)
     * @param accel_y 加速度计Y轴 (g)
     * @param accel_z 加速度计Z轴 (g)
     * @param gyro_x 陀螺仪X轴 (度/秒)
     * @param gyro_y 陀螺仪Y轴 (度/秒)
     * @param gyro_z 陀螺仪Z轴 (度/秒)
     * @return Fusion_Status 更新状态
     */
    Fusion_Status update(float accel_x, float accel_y, float accel_z,
                        float gyro_x, float gyro_y, float gyro_z);

    /**
     * @brief 获取四元数
     * @param quat 输出的四元数数据
     * @return Fusion_Status 获取状态
     */
    Fusion_Status getQuaternion(Quaternion_Data& quat);

    /**
     * @brief 获取姿态角
     * @param attitude 输出的姿态数据
     * @return Fusion_Status 获取状态
     */
    Fusion_Status getAttitude(Attitude_Data& attitude);

    /**
     * @brief 获取欧拉角 (度)
     * @param roll 横滚角
     * @param pitch 俯仰角
     * @param yaw 偏航角
     * @return Fusion_Status 获取状态
     */
    Fusion_Status getEulerAngles(float& roll, float& pitch, float& yaw);

    /**
     * @brief 获取航向角 (度)
     * @return float 航向角
     */
    float getHeading();

    /**
     * @brief 重置融合算法
     * @return Fusion_Status 重置状态
     */
    Fusion_Status reset();

    /**
     * @brief 设置融合配置
     * @param config 新的配置参数
     * @return Fusion_Status 设置状态
     */
    Fusion_Status setConfig(const Fusion_Config& config);

    /**
     * @brief 获取当前配置
     * @param config 输出的配置参数
     * @return Fusion_Status 获取状态
     */
    Fusion_Status getConfig(Fusion_Config& config);

    /**
     * @brief 获取当前状态
     * @return Fusion_Status 当前状态
     */
    Fusion_Status getStatus() const;

    /**
     * @brief 获取采样计数
     * @return uint32_t 采样计数
     */
    uint32_t getSampleCount() const;

    /**
     * @brief 获取最后一次错误信息
     * @return const char* 错误信息字符串
     */
    const char* getLastError() const;

    /**
     * @brief 获取统计信息
     * @param max_accel 最大加速度幅值
     * @param max_gyro 最大角速度幅值
     */
    void getStatistics(float& max_accel, float& max_gyro);

    /**
     * @brief 清除统计信息
     */
    void clearStatistics();

    /**
     * @brief 检查是否已初始化
     * @return bool 初始化状态
     */
    bool isInitialized() const;
};

#endif // IMU_FUSION_ENGINE_H
