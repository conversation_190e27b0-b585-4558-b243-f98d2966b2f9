{"artifacts": [{"path": "Arduino_ESP32C3_IMU.exe"}, {"path": "Arduino_ESP32C3_IMU.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 9, "parent": 0}, {"command": 1, "file": 0, "line": 6, "parent": 0}, {"command": 1, "file": 0, "line": 7, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++14 -fdiagnostics-color=always"}], "includes": [{"backtrace": 2, "path": "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion"}, {"backtrace": 3, "path": "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "14"}, "sourceIndexes": [0]}, {"compileCommandFragments": [{"fragment": "-g -fdiagnostics-color=always"}], "includes": [{"backtrace": 2, "path": "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion"}, {"backtrace": 3, "path": "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src"}], "language": "C", "sourceIndexes": [2, 4, 8, 12]}], "id": "Arduino_ESP32C3_IMU::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "Arduino_ESP32C3_IMU", "nameOnDisk": "Arduino_ESP32C3_IMU.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 4, 8, 12]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 5, 6, 7, 9, 10, 11, 13]}, {"name": "", "sourceIndexes": [14]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "ESP32C3_IMU_Fusion/IMU_Driver.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "ESP32C3_IMU_Fusion/IMU_Driver.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "ESP32C3_IMU_Fusion/main.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libraries/Fusion/src/Fusion.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "libraries/Fusion/src/FusionAhrs.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libraries/Fusion/src/FusionAhrs.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libraries/Fusion/src/FusionAxes.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libraries/Fusion/src/FusionCalibration.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "libraries/Fusion/src/FusionCompass.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libraries/Fusion/src/FusionCompass.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libraries/Fusion/src/FusionConvention.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libraries/Fusion/src/FusionMath.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "libraries/Fusion/src/FusionOffset.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libraries/Fusion/src/FusionOffset.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "ESP32C3_IMU_Fusion/ESP32C3_IMU_Fusion.ino", "sourceGroupIndex": 2}], "type": "EXECUTABLE"}