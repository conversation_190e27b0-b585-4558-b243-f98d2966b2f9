/**
 * @file imu_sensor_interface.h
 * @brief IMU传感器抽象接口定义 (纯C实现)
 * <AUTHOR> IMU Fusion Project
 * @version 2.0
 * @date 2025-01-02
 * 
 * 使用纯C结构体和函数指针实现传感器抽象接口
 * 支持不同型号的IMU传感器统一管理
 */

#ifndef IMU_SENSOR_INTERFACE_H
#define IMU_SENSOR_INTERFACE_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief IMU传感器数据结构
 */
typedef struct {
    float accel_x;      ///< 加速度计X轴 (g)
    float accel_y;      ///< 加速度计Y轴 (g)
    float accel_z;      ///< 加速度计Z轴 (g)
    float gyro_x;       ///< 陀螺仪X轴 (度/秒)
    float gyro_y;       ///< 陀螺仪Y轴 (度/秒)
    float gyro_z;       ///< 陀螺仪Z轴 (度/秒)
    float temperature;  ///< 温度 (摄氏度)
    uint64_t timestamp; ///< 时间戳 (微秒)
} imu_data_t;

/**
 * @brief IMU传感器原始数据结构
 */
typedef struct {
    int16_t accel_raw[3];   ///< 加速度计原始数据 [x, y, z]
    int16_t gyro_raw[3];    ///< 陀螺仪原始数据 [x, y, z]
    int16_t temp_raw;       ///< 温度原始数据
    uint64_t timestamp;     ///< 时间戳 (微秒)
} imu_raw_data_t;

/**
 * @brief IMU传感器配置结构
 */
typedef struct {
    uint16_t accel_range;       ///< 加速度计量程 (g)
    uint16_t gyro_range;        ///< 陀螺仪量程 (度/秒)
    uint16_t sample_rate;       ///< 采样率 (Hz)
    bool enable_filter;         ///< 是否启用数字滤波器
    uint8_t filter_bandwidth;   ///< 滤波器带宽
} imu_config_t;

/**
 * @brief IMU传感器类型枚举
 */
typedef enum {
    IMU_TYPE_UNKNOWN = 0,
    IMU_TYPE_SH5001,
    IMU_TYPE_MPU6050,
    IMU_TYPE_MPU6500,
    IMU_TYPE_MPU9250,
    IMU_TYPE_LSM6DS3,
    IMU_TYPE_LSM6DSO,
    IMU_TYPE_ICM20602,
    IMU_TYPE_ICM20948
} imu_type_t;

// 前向声明
typedef struct imu_sensor_s imu_sensor_t;

/**
 * @brief IMU传感器操作函数指针结构
 */
typedef struct {
    /**
     * @brief 初始化传感器
     */
    esp_err_t (*init)(imu_sensor_t *sensor, const imu_config_t *config);
    
    /**
     * @brief 读取IMU数据
     */
    esp_err_t (*read_data)(imu_sensor_t *sensor, imu_data_t *data);
    
    /**
     * @brief 读取原始数据
     */
    esp_err_t (*read_raw_data)(imu_sensor_t *sensor, imu_raw_data_t *raw_data);
    
    /**
     * @brief 检查连接状态
     */
    bool (*is_connected)(imu_sensor_t *sensor);
    
    /**
     * @brief 获取设备ID
     */
    uint8_t (*get_device_id)(imu_sensor_t *sensor);
    
    /**
     * @brief 软件复位
     */
    esp_err_t (*reset)(imu_sensor_t *sensor);
    
    /**
     * @brief 校准传感器
     */
    esp_err_t (*calibrate)(imu_sensor_t *sensor, uint32_t calibration_time_ms);
    
    /**
     * @brief 设置配置
     */
    esp_err_t (*set_config)(imu_sensor_t *sensor, const imu_config_t *config);
    
    /**
     * @brief 获取配置
     */
    esp_err_t (*get_config)(imu_sensor_t *sensor, imu_config_t *config);
    
    /**
     * @brief 获取传感器信息
     */
    const char* (*get_info)(imu_sensor_t *sensor);
    
    /**
     * @brief 销毁传感器实例
     */
    void (*destroy)(imu_sensor_t *sensor);
} imu_sensor_ops_t;

/**
 * @brief IMU传感器实例结构
 */
struct imu_sensor_s {
    imu_type_t type;                    ///< 传感器类型
    const imu_sensor_ops_t *ops;        ///< 操作函数指针
    void *priv_data;                    ///< 私有数据指针
    imu_config_t config;                ///< 当前配置
    char info_buffer[256];              ///< 信息缓冲区
    bool initialized;                   ///< 初始化标志
};

/**
 * @brief 创建IMU传感器实例
 * @param type 传感器类型
 * @param priv_data 私有数据指针
 * @return 传感器实例指针，失败返回NULL
 */
imu_sensor_t* imu_sensor_create(imu_type_t type, void *priv_data);

/**
 * @brief 销毁IMU传感器实例
 * @param sensor 传感器实例指针
 */
void imu_sensor_destroy(imu_sensor_t *sensor);

/**
 * @brief 初始化IMU传感器
 * @param sensor 传感器实例指针
 * @param config 配置参数，NULL使用默认配置
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_sensor_init(imu_sensor_t *sensor, const imu_config_t *config);

/**
 * @brief 读取IMU数据
 * @param sensor 传感器实例指针
 * @param data 输出的IMU数据
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_sensor_read_data(imu_sensor_t *sensor, imu_data_t *data);

/**
 * @brief 读取原始数据
 * @param sensor 传感器实例指针
 * @param raw_data 输出的原始数据
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_sensor_read_raw_data(imu_sensor_t *sensor, imu_raw_data_t *raw_data);

/**
 * @brief 检查传感器连接状态
 * @param sensor 传感器实例指针
 * @return true连接正常，false连接异常
 */
bool imu_sensor_is_connected(imu_sensor_t *sensor);

/**
 * @brief 获取传感器类型
 * @param sensor 传感器实例指针
 * @return 传感器类型
 */
imu_type_t imu_sensor_get_type(imu_sensor_t *sensor);

/**
 * @brief 获取设备ID
 * @param sensor 传感器实例指针
 * @return 设备ID
 */
uint8_t imu_sensor_get_device_id(imu_sensor_t *sensor);

/**
 * @brief 软件复位传感器
 * @param sensor 传感器实例指针
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_sensor_reset(imu_sensor_t *sensor);

/**
 * @brief 校准传感器
 * @param sensor 传感器实例指针
 * @param calibration_time_ms 校准时间 (毫秒)
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_sensor_calibrate(imu_sensor_t *sensor, uint32_t calibration_time_ms);

/**
 * @brief 设置传感器配置
 * @param sensor 传感器实例指针
 * @param config 配置参数
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_sensor_set_config(imu_sensor_t *sensor, const imu_config_t *config);

/**
 * @brief 获取传感器配置
 * @param sensor 传感器实例指针
 * @param config 输出的配置参数
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_sensor_get_config(imu_sensor_t *sensor, imu_config_t *config);

/**
 * @brief 获取传感器信息
 * @param sensor 传感器实例指针
 * @return 传感器信息字符串
 */
const char* imu_sensor_get_info(imu_sensor_t *sensor);

/**
 * @brief 创建默认传感器配置
 * @param accel_range 加速度计量程 (g)
 * @param gyro_range 陀螺仪量程 (度/秒)
 * @param sample_rate 采样率 (Hz)
 * @return 默认配置
 */
imu_config_t imu_sensor_create_default_config(uint16_t accel_range, uint16_t gyro_range, uint16_t sample_rate);

#ifdef __cplusplus
}
#endif

#endif // IMU_SENSOR_INTERFACE_H
