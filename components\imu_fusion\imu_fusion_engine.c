/**
 * @file imu_fusion_engine.c
 * @brief IMU数据融合引擎实现文件 (纯C实现)
 * <AUTHOR> IMU Fusion Project
 * @version 2.0
 * @date 2025-01-02
 */

#include "imu_fusion_engine.h"
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "esp_log.h"
#include "esp_timer.h"

static const char *TAG = "imu_fusion_engine";

// 默认配置常量
#define DEFAULT_GAIN                    0.5f
#define DEFAULT_GYRO_RANGE             2000.0f
#define DEFAULT_ACCEL_REJECTION        10.0f
#define DEFAULT_MAGNETIC_REJECTION     10.0f
#define DEFAULT_SAMPLE_PERIOD          0.01f  // 100Hz
#define DEFAULT_RECOVERY_PERIOD        500    // 5秒 @ 100Hz

#define MAX_ACCEL_THRESHOLD            50.0f  // 最大合理加速度 (g)
#define MAX_GYRO_THRESHOLD             3000.0f // 最大合理角速度 (度/秒)

// 私有函数声明
static bool validate_imu_data(const imu_data_t *data);
static void update_statistics(imu_fusion_engine_t *engine, const imu_data_t *data);
static void update_quaternion_cache(imu_fusion_engine_t *engine);
static void update_attitude_cache(imu_fusion_engine_t *engine);

imu_fusion_engine_t* imu_fusion_engine_create(void)
{
    imu_fusion_engine_t *engine = calloc(1, sizeof(imu_fusion_engine_t));
    if (!engine) {
        ESP_LOGE(TAG, "Failed to allocate memory for fusion engine");
        return NULL;
    }

    // 设置默认配置
    engine->config.convention = FusionConventionNwu;
    engine->config.gain = DEFAULT_GAIN;
    engine->config.gyroscope_range = DEFAULT_GYRO_RANGE;
    engine->config.acceleration_rejection = DEFAULT_ACCEL_REJECTION;
    engine->config.magnetic_rejection = DEFAULT_MAGNETIC_REJECTION;
    engine->config.recovery_trigger_period = DEFAULT_RECOVERY_PERIOD;
    engine->config.sample_period = DEFAULT_SAMPLE_PERIOD;
    engine->config.use_magnetometer = false;

    // 初始化四元数为单位四元数
    engine->quaternion.w = 1.0f;
    engine->quaternion.x = 0.0f;
    engine->quaternion.y = 0.0f;
    engine->quaternion.z = 0.0f;

    engine->initialized = false;
    engine->sample_count = 0;
    engine->last_update_time = 0;
    engine->max_accel_magnitude = 0.0f;
    engine->max_gyro_magnitude = 0.0f;

    ESP_LOGI(TAG, "Created fusion engine instance");
    return engine;
}

void imu_fusion_engine_destroy(imu_fusion_engine_t *engine)
{
    if (engine) {
        free(engine);
        ESP_LOGI(TAG, "Destroyed fusion engine instance");
    }
}

esp_err_t imu_fusion_engine_init(imu_fusion_engine_t *engine, const fusion_config_t *config)
{
    if (!engine) {
        ESP_LOGE(TAG, "Invalid engine pointer");
        return ESP_ERR_INVALID_ARG;
    }

    // 更新配置
    if (config) {
        engine->config = *config;
    }

    // 初始化AHRS算法
    FusionAhrsInitialise(&engine->ahrs);

    // 设置AHRS算法参数
    const FusionAhrsSettings settings = {
        .convention = engine->config.convention,
        .gain = engine->config.gain,
        .gyroscopeRange = engine->config.gyroscope_range,
        .accelerationRejection = engine->config.acceleration_rejection,
        .magneticRejection = engine->config.magnetic_rejection,
        .recoveryTriggerPeriod = engine->config.recovery_trigger_period,
    };

    FusionAhrsSetSettings(&engine->ahrs, &settings);

    // 重置统计信息
    engine->sample_count = 0;
    engine->max_accel_magnitude = 0.0f;
    engine->max_gyro_magnitude = 0.0f;
    engine->last_update_time = esp_timer_get_time();

    engine->initialized = true;

    ESP_LOGI(TAG, "Fusion engine initialized successfully");
    ESP_LOGI(TAG, "Config: gain=%.2f, gyro_range=%.0f, sample_period=%.3f",
             engine->config.gain, engine->config.gyroscope_range, engine->config.sample_period);

    return ESP_OK;
}

esp_err_t imu_fusion_engine_update(imu_fusion_engine_t *engine, const imu_data_t *data)
{
    if (!engine || !data) {
        ESP_LOGE(TAG, "Invalid parameters");
        return ESP_ERR_INVALID_ARG;
    }

    if (!engine->initialized) {
        ESP_LOGE(TAG, "Fusion engine not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    // 验证数据有效性
    if (!validate_imu_data(data)) {
        ESP_LOGW(TAG, "Invalid IMU data detected");
        return ESP_ERR_INVALID_ARG;
    }

    // 准备Fusion库所需的数据格式
    const FusionVector gyroscope = {
        .axis = {data->gyro_x, data->gyro_y, data->gyro_z}
    };
    const FusionVector accelerometer = {
        .axis = {data->accel_x, data->accel_y, data->accel_z}
    };

    // 更新AHRS算法
    if (engine->config.use_magnetometer) {
        // 如果有磁力计数据，这里可以扩展
        FusionAhrsUpdateNoMagnetometer(&engine->ahrs, gyroscope, accelerometer, engine->config.sample_period);
    } else {
        FusionAhrsUpdateNoMagnetometer(&engine->ahrs, gyroscope, accelerometer, engine->config.sample_period);
    }

    // 更新缓存的结果
    update_quaternion_cache(engine);
    update_attitude_cache(engine);

    // 更新统计信息
    update_statistics(engine, data);

    engine->sample_count++;
    engine->last_update_time = esp_timer_get_time();

    return ESP_OK;
}

esp_err_t imu_fusion_engine_update_manual(imu_fusion_engine_t *engine,
                                         float accel_x, float accel_y, float accel_z,
                                         float gyro_x, float gyro_y, float gyro_z)
{
    imu_data_t data = {
        .accel_x = accel_x,
        .accel_y = accel_y,
        .accel_z = accel_z,
        .gyro_x = gyro_x,
        .gyro_y = gyro_y,
        .gyro_z = gyro_z,
        .temperature = 0.0f,
        .timestamp = esp_timer_get_time()
    };

    return imu_fusion_engine_update(engine, &data);
}

esp_err_t imu_fusion_engine_get_quaternion(imu_fusion_engine_t *engine, quaternion_data_t *quat)
{
    if (!engine || !quat) {
        return ESP_ERR_INVALID_ARG;
    }

    if (!engine->initialized) {
        ESP_LOGE(TAG, "Fusion engine not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    *quat = engine->quaternion;
    return ESP_OK;
}

esp_err_t imu_fusion_engine_get_attitude(imu_fusion_engine_t *engine, attitude_data_t *attitude)
{
    if (!engine || !attitude) {
        return ESP_ERR_INVALID_ARG;
    }

    if (!engine->initialized) {
        ESP_LOGE(TAG, "Fusion engine not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    *attitude = engine->attitude;
    return ESP_OK;
}

esp_err_t imu_fusion_engine_get_euler_angles(imu_fusion_engine_t *engine, 
                                            float *roll, float *pitch, float *yaw)
{
    if (!engine || !roll || !pitch || !yaw) {
        return ESP_ERR_INVALID_ARG;
    }

    if (!engine->initialized) {
        ESP_LOGE(TAG, "Fusion engine not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    *roll = engine->attitude.roll;
    *pitch = engine->attitude.pitch;
    *yaw = engine->attitude.yaw;

    return ESP_OK;
}

float imu_fusion_engine_get_heading(imu_fusion_engine_t *engine)
{
    if (!engine || !engine->initialized) {
        return 0.0f;
    }
    return engine->attitude.heading;
}

esp_err_t imu_fusion_engine_reset(imu_fusion_engine_t *engine)
{
    if (!engine) {
        return ESP_ERR_INVALID_ARG;
    }

    if (!engine->initialized) {
        ESP_LOGE(TAG, "Fusion engine not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    // 重新初始化AHRS算法
    FusionAhrsInitialise(&engine->ahrs);

    const FusionAhrsSettings settings = {
        .convention = engine->config.convention,
        .gain = engine->config.gain,
        .gyroscopeRange = engine->config.gyroscope_range,
        .accelerationRejection = engine->config.acceleration_rejection,
        .magneticRejection = engine->config.magnetic_rejection,
        .recoveryTriggerPeriod = engine->config.recovery_trigger_period,
    };

    FusionAhrsSetSettings(&engine->ahrs, &settings);

    // 重置数据
    memset(&engine->attitude, 0, sizeof(engine->attitude));
    engine->quaternion.w = 1.0f;
    engine->quaternion.x = 0.0f;
    engine->quaternion.y = 0.0f;
    engine->quaternion.z = 0.0f;

    engine->sample_count = 0;
    engine->last_update_time = esp_timer_get_time();

    ESP_LOGI(TAG, "Fusion engine reset successfully");
    return ESP_OK;
}

// 私有函数实现
static bool validate_imu_data(const imu_data_t *data)
{
    // 检查加速度计数据
    float accel_mag = sqrtf(data->accel_x * data->accel_x + 
                           data->accel_y * data->accel_y + 
                           data->accel_z * data->accel_z);
    
    if (accel_mag > MAX_ACCEL_THRESHOLD || accel_mag < 0.1f) {
        return false;
    }
    
    // 检查陀螺仪数据
    float gyro_mag = sqrtf(data->gyro_x * data->gyro_x + 
                          data->gyro_y * data->gyro_y + 
                          data->gyro_z * data->gyro_z);
    
    if (gyro_mag > MAX_GYRO_THRESHOLD) {
        return false;
    }
    
    // 检查NaN和无穷大
    if (isnan(data->accel_x) || isnan(data->accel_y) || isnan(data->accel_z) ||
        isnan(data->gyro_x) || isnan(data->gyro_y) || isnan(data->gyro_z) ||
        isinf(data->accel_x) || isinf(data->accel_y) || isinf(data->accel_z) ||
        isinf(data->gyro_x) || isinf(data->gyro_y) || isinf(data->gyro_z)) {
        return false;
    }
    
    return true;
}

static void update_statistics(imu_fusion_engine_t *engine, const imu_data_t *data)
{
    // 更新最大加速度幅值
    float accel_mag = sqrtf(data->accel_x * data->accel_x + 
                           data->accel_y * data->accel_y + 
                           data->accel_z * data->accel_z);
    if (accel_mag > engine->max_accel_magnitude) {
        engine->max_accel_magnitude = accel_mag;
    }
    
    // 更新最大角速度幅值
    float gyro_mag = sqrtf(data->gyro_x * data->gyro_x + 
                          data->gyro_y * data->gyro_y + 
                          data->gyro_z * data->gyro_z);
    if (gyro_mag > engine->max_gyro_magnitude) {
        engine->max_gyro_magnitude = gyro_mag;
    }
}

static void update_quaternion_cache(imu_fusion_engine_t *engine)
{
    const FusionQuaternion q = FusionAhrsGetQuaternion(&engine->ahrs);
    engine->quaternion.w = q.element.w;
    engine->quaternion.x = q.element.x;
    engine->quaternion.y = q.element.y;
    engine->quaternion.z = q.element.z;
}

static void update_attitude_cache(imu_fusion_engine_t *engine)
{
    const FusionEuler euler = FusionQuaternionToEuler(FusionAhrsGetQuaternion(&engine->ahrs));
    engine->attitude.roll = euler.angle.roll;
    engine->attitude.pitch = euler.angle.pitch;
    engine->attitude.yaw = euler.angle.yaw;
    engine->attitude.heading = engine->attitude.yaw; // 简化处理
}

esp_err_t imu_fusion_engine_set_config(imu_fusion_engine_t *engine, const fusion_config_t *config)
{
    if (!engine || !config) {
        return ESP_ERR_INVALID_ARG;
    }

    engine->config = *config;

    if (engine->initialized) {
        // 重新初始化以应用新配置
        return imu_fusion_engine_init(engine, config);
    }

    return ESP_OK;
}

esp_err_t imu_fusion_engine_get_config(imu_fusion_engine_t *engine, fusion_config_t *config)
{
    if (!engine || !config) {
        return ESP_ERR_INVALID_ARG;
    }

    *config = engine->config;
    return ESP_OK;
}

uint32_t imu_fusion_engine_get_sample_count(imu_fusion_engine_t *engine)
{
    if (!engine) {
        return 0;
    }
    return engine->sample_count;
}

void imu_fusion_engine_get_statistics(imu_fusion_engine_t *engine, float *max_accel, float *max_gyro)
{
    if (!engine || !max_accel || !max_gyro) {
        return;
    }

    *max_accel = engine->max_accel_magnitude;
    *max_gyro = engine->max_gyro_magnitude;
}

void imu_fusion_engine_clear_statistics(imu_fusion_engine_t *engine)
{
    if (!engine) {
        return;
    }

    engine->max_accel_magnitude = 0.0f;
    engine->max_gyro_magnitude = 0.0f;
    engine->sample_count = 0;
}

bool imu_fusion_engine_is_initialized(imu_fusion_engine_t *engine)
{
    if (!engine) {
        return false;
    }
    return engine->initialized;
}

fusion_config_t imu_fusion_engine_create_default_config(float sample_rate)
{
    fusion_config_t config = {
        .convention = FusionConventionNwu,
        .gain = DEFAULT_GAIN,
        .gyroscope_range = DEFAULT_GYRO_RANGE,
        .acceleration_rejection = DEFAULT_ACCEL_REJECTION,
        .magnetic_rejection = DEFAULT_MAGNETIC_REJECTION,
        .recovery_trigger_period = (uint32_t)(5.0f * sample_rate), // 5秒
        .sample_period = 1.0f / sample_rate,
        .use_magnetometer = false
    };

    return config;
}

void imu_fusion_engine_print_quaternion(imu_fusion_engine_t *engine, int precision)
{
    if (!engine || !engine->initialized) {
        printf("Fusion engine not initialized\n");
        return;
    }

    printf("%.*f,%.*f,%.*f,%.*f\n",
           precision, engine->quaternion.w,
           precision, engine->quaternion.x,
           precision, engine->quaternion.y,
           precision, engine->quaternion.z);
}

void imu_fusion_engine_print_euler_angles(imu_fusion_engine_t *engine, int precision)
{
    if (!engine || !engine->initialized) {
        printf("Fusion engine not initialized\n");
        return;
    }

    printf("Roll: %.*f°, Pitch: %.*f°, Yaw: %.*f°\n",
           precision, engine->attitude.roll,
           precision, engine->attitude.pitch,
           precision, engine->attitude.yaw);
}
