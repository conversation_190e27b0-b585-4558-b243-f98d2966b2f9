name=IMU Fusion Library
version=1.0.0
author=ESP32-C3 IMU Fusion Project
maintainer=ESP32-C3 IMU Fusion Project
sentence=Universal IMU sensor fusion library for ESP32
paragraph=A comprehensive IMU data fusion library that supports multiple IMU sensors (SH5001, MPU6050, LSM6DS3, etc.) and provides easy-to-use APIs for attitude estimation using the Fusion AHRS algorithm. Features include automatic sensor detection, calibration, and real-time orientation tracking.
category=Sensors
url=https://github.com/esp32-imu-fusion
architectures=esp32
depends=Fusion
