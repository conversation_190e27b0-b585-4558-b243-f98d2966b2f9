# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.30

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: Arduino_ESP32C3_IMU
# Configurations: 
# =============================================================================
# =============================================================================

#############################################
# Rule for compiling C files.

rule C_COMPILER__Arduino_ESP32C3_IMU_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\Users\<USER>\scoop\apps\mingw-winlibs\current\bin\gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__Arduino_ESP32C3_IMU_unscanned_
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\Users\<USER>\scoop\apps\mingw-winlibs\current\bin\c++.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__Arduino_ESP32C3_IMU_
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && C:\Users\<USER>\scoop\apps\mingw-winlibs\current\bin\c++.exe $FLAGS $LINK_FLAGS $in -o $TARGET_FILE -Wl,--out-implib,$TARGET_IMPLIB -Wl,--major-image-version,0,--minor-image-version,0 $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking CXX executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = D:\esp\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\Arduino_ESP32C3_IMU -BC:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\Arduino_ESP32C3_IMU\build
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = D:\esp\tools\tools\ninja\1.12.1\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = D:\esp\tools\tools\ninja\1.12.1\ninja.exe -t targets
  description = All primary targets available:

