/**
 * @file sh5001_sensor.c
 * @brief SH5001 IMU传感器驱动实现文件 (纯C实现)
 * <AUTHOR> IMU Fusion Project
 * @version 2.0
 * @date 2025-01-02
 */

#include "sh5001_sensor.h"
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "sh5001";

// 私有函数声明
static esp_err_t sh5001_init(imu_sensor_t *sensor, const imu_config_t *config);
static esp_err_t sh5001_read_data(imu_sensor_t *sensor, imu_data_t *data);
static esp_err_t sh5001_read_raw_data(imu_sensor_t *sensor, imu_raw_data_t *raw_data);
static bool sh5001_is_connected(imu_sensor_t *sensor);
static uint8_t sh5001_get_device_id(imu_sensor_t *sensor);
static esp_err_t sh5001_reset(imu_sensor_t *sensor);
static esp_err_t sh5001_calibrate(imu_sensor_t *sensor, uint32_t calibration_time_ms);
static esp_err_t sh5001_set_config(imu_sensor_t *sensor, const imu_config_t *config);
static esp_err_t sh5001_get_config(imu_sensor_t *sensor, imu_config_t *config);
static const char* sh5001_get_info(imu_sensor_t *sensor);
static void sh5001_destroy(imu_sensor_t *sensor);

// 配置函数声明
static esp_err_t sh5001_configure_accelerometer(sh5001_priv_data_t *priv, const imu_config_t *config);
static esp_err_t sh5001_configure_gyroscope(sh5001_priv_data_t *priv, const imu_config_t *config);
static esp_err_t sh5001_configure_temperature(sh5001_priv_data_t *priv);
static void sh5001_update_scale_factors(sh5001_priv_data_t *priv, const imu_config_t *config);
static uint8_t sh5001_map_accel_range(uint16_t range);
static uint8_t sh5001_map_gyro_range(uint16_t range);
static uint8_t sh5001_map_sample_rate(uint16_t rate);

// I2C操作函数
static esp_err_t sh5001_write_register(sh5001_priv_data_t *priv, uint8_t reg, uint8_t value);
static esp_err_t sh5001_read_register(sh5001_priv_data_t *priv, uint8_t reg, uint8_t *value);
static esp_err_t sh5001_read_registers(sh5001_priv_data_t *priv, uint8_t reg, uint8_t *buffer, size_t length);
static int16_t sh5001_combine_bytes(uint8_t high, uint8_t low);

// 配置函数
static esp_err_t sh5001_configure_accelerometer(sh5001_priv_data_t *priv, const imu_config_t *config);
static esp_err_t sh5001_configure_gyroscope(sh5001_priv_data_t *priv, const imu_config_t *config);
static esp_err_t sh5001_configure_temperature(sh5001_priv_data_t *priv);
static void sh5001_update_scale_factors(sh5001_priv_data_t *priv, const imu_config_t *config);
static uint8_t sh5001_map_accel_range(uint16_t range);
static uint8_t sh5001_map_gyro_range(uint16_t range);
static uint8_t sh5001_map_sample_rate(uint16_t rate);

// 操作函数指针结构
static const imu_sensor_ops_t sh5001_ops = {
    .init = sh5001_init,
    .read_data = sh5001_read_data,
    .read_raw_data = sh5001_read_raw_data,
    .is_connected = sh5001_is_connected,
    .get_device_id = sh5001_get_device_id,
    .reset = sh5001_reset,
    .calibrate = sh5001_calibrate,
    .set_config = sh5001_set_config,
    .get_config = sh5001_get_config,
    .get_info = sh5001_get_info,
    .destroy = sh5001_destroy
};

const imu_sensor_ops_t* sh5001_get_ops(void)
{
    return &sh5001_ops;
}

imu_sensor_t* sh5001_create(const sh5001_init_config_t *init_config)
{
    if (!init_config) {
        ESP_LOGE(TAG, "Invalid init config");
        return NULL;
    }

    // 分配私有数据
    sh5001_priv_data_t *priv = calloc(1, sizeof(sh5001_priv_data_t));
    if (!priv) {
        ESP_LOGE(TAG, "Failed to allocate private data");
        return NULL;
    }

    // 初始化私有数据
    priv->i2c_port = init_config->i2c_port;
    priv->device_address = init_config->device_address;
    priv->gyro_scale = 1.0f;
    priv->accel_scale = 1.0f;
    priv->connection_verified = false;
    memset(priv->accel_offset, 0, sizeof(priv->accel_offset));
    memset(priv->gyro_offset, 0, sizeof(priv->gyro_offset));

    // 配置I2C
    i2c_config_t i2c_config = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = init_config->sda_pin,
        .scl_io_num = init_config->scl_pin,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = init_config->i2c_freq,
    };

    esp_err_t ret = i2c_param_config(priv->i2c_port, &i2c_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C param config failed: %s", esp_err_to_name(ret));
        free(priv);
        return NULL;
    }

    ret = i2c_driver_install(priv->i2c_port, I2C_MODE_MASTER, 0, 0, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C driver install failed: %s", esp_err_to_name(ret));
        free(priv);
        return NULL;
    }

    // 创建传感器实例
    imu_sensor_t *sensor = imu_sensor_create(IMU_TYPE_SH5001, priv);
    if (!sensor) {
        ESP_LOGE(TAG, "Failed to create sensor instance");
        i2c_driver_delete(priv->i2c_port);
        free(priv);
        return NULL;
    }

    ESP_LOGI(TAG, "SH5001 sensor created successfully");
    return sensor;
}

sh5001_init_config_t sh5001_create_default_init_config(i2c_port_t i2c_port, int sda_pin, int scl_pin)
{
    sh5001_init_config_t config = {
        .i2c_port = i2c_port,
        .sda_pin = sda_pin,
        .scl_pin = scl_pin,
        .i2c_freq = 400000,
        .device_address = SH5001_ADDR
    };
    return config;
}

imu_sensor_t* sh5001_auto_detect(i2c_port_t i2c_port, int sda_pin, int scl_pin, uint32_t i2c_freq)
{
    ESP_LOGI(TAG, "Auto-detecting SH5001 sensor...");

    // 尝试主地址
    sh5001_init_config_t config = {
        .i2c_port = i2c_port,
        .sda_pin = sda_pin,
        .scl_pin = scl_pin,
        .i2c_freq = i2c_freq,
        .device_address = SH5001_ADDR
    };

    imu_sensor_t *sensor = sh5001_create(&config);
    if (sensor && sh5001_is_connected(sensor)) {
        ESP_LOGI(TAG, "SH5001 detected at address 0x%02X", SH5001_ADDR);
        return sensor;
    }
    if (sensor) {
        imu_sensor_destroy(sensor);
    }

    // 尝试备用地址
    config.device_address = SH5001_ADDR_ALT;
    sensor = sh5001_create(&config);
    if (sensor && sh5001_is_connected(sensor)) {
        ESP_LOGI(TAG, "SH5001 detected at address 0x%02X", SH5001_ADDR_ALT);
        return sensor;
    }
    if (sensor) {
        imu_sensor_destroy(sensor);
    }

    ESP_LOGW(TAG, "SH5001 not detected");
    return NULL;
}

// 实现操作函数
static esp_err_t sh5001_init(imu_sensor_t *sensor, const imu_config_t *config)
{
    if (!sensor || !sensor->priv_data) {
        return ESP_ERR_INVALID_ARG;
    }

    sh5001_priv_data_t *priv = (sh5001_priv_data_t*)sensor->priv_data;

    // 使用默认配置如果没有提供
    imu_config_t default_config;
    if (!config) {
        default_config = imu_sensor_create_default_config(16, 2000, 125);
        config = &default_config;
    }

    // 检查设备连接
    if (!sh5001_is_connected(sensor)) {
        ESP_LOGE(TAG, "SH5001 device not found on I2C bus");
        return ESP_ERR_NOT_FOUND;
    }

    // 软件复位
    esp_err_t ret = sh5001_reset(sensor);
    if (ret != ESP_OK) {
        return ret;
    }

    vTaskDelay(pdMS_TO_TICKS(50)); // 等待复位完成

    // 配置传感器
    ret = sh5001_configure_accelerometer(priv, config);
    if (ret != ESP_OK) {
        return ret;
    }

    ret = sh5001_configure_gyroscope(priv, config);
    if (ret != ESP_OK) {
        return ret;
    }

    ret = sh5001_configure_temperature(priv);
    if (ret != ESP_OK) {
        return ret;
    }

    // 更新量程转换因子
    sh5001_update_scale_factors(priv, config);

    priv->connection_verified = true;
    sensor->config = *config;

    ESP_LOGI(TAG, "SH5001 initialized successfully");
    return ESP_OK;
}

static esp_err_t sh5001_read_data(imu_sensor_t *sensor, imu_data_t *data)
{
    if (!sensor || !data || !sensor->priv_data) {
        return ESP_ERR_INVALID_ARG;
    }

    sh5001_priv_data_t *priv = (sh5001_priv_data_t*)sensor->priv_data;

    if (!priv->connection_verified) {
        ESP_LOGE(TAG, "SH5001 not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    // 读取原始数据
    imu_raw_data_t raw_data;
    esp_err_t ret = sh5001_read_raw_data(sensor, &raw_data);
    if (ret != ESP_OK) {
        return ret;
    }

    // 转换为物理单位并应用校准偏移
    data->accel_x = (raw_data.accel_raw[0] * priv->accel_scale) - priv->accel_offset[0];
    data->accel_y = (raw_data.accel_raw[1] * priv->accel_scale) - priv->accel_offset[1];
    data->accel_z = (raw_data.accel_raw[2] * priv->accel_scale) - priv->accel_offset[2];

    data->gyro_x = (raw_data.gyro_raw[0] * priv->gyro_scale) - priv->gyro_offset[0];
    data->gyro_y = (raw_data.gyro_raw[1] * priv->gyro_scale) - priv->gyro_offset[1];
    data->gyro_z = (raw_data.gyro_raw[2] * priv->gyro_scale) - priv->gyro_offset[2];

    data->temperature = raw_data.temp_raw * 0.0625f; // SH5001温度转换因子
    data->timestamp = esp_timer_get_time();

    return ESP_OK;
}

static esp_err_t sh5001_read_raw_data(imu_sensor_t *sensor, imu_raw_data_t *raw_data)
{
    if (!sensor || !raw_data || !sensor->priv_data) {
        return ESP_ERR_INVALID_ARG;
    }

    sh5001_priv_data_t *priv = (sh5001_priv_data_t*)sensor->priv_data;

    if (!priv->connection_verified) {
        ESP_LOGE(TAG, "SH5001 not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    uint8_t buffer[14]; // 6字节加速度 + 6字节陀螺仪 + 2字节温度

    // 读取所有传感器数据
    esp_err_t ret = sh5001_read_registers(priv, SH5001_ACC_XL, buffer, 14);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to read sensor data from SH5001");
        return ret;
    }

    // 解析加速度计数据
    raw_data->accel_raw[0] = sh5001_combine_bytes(buffer[1], buffer[0]);  // X轴
    raw_data->accel_raw[1] = sh5001_combine_bytes(buffer[3], buffer[2]);  // Y轴
    raw_data->accel_raw[2] = sh5001_combine_bytes(buffer[5], buffer[4]);  // Z轴

    // 解析陀螺仪数据
    raw_data->gyro_raw[0] = sh5001_combine_bytes(buffer[7], buffer[6]);   // X轴
    raw_data->gyro_raw[1] = sh5001_combine_bytes(buffer[9], buffer[8]);   // Y轴
    raw_data->gyro_raw[2] = sh5001_combine_bytes(buffer[11], buffer[10]); // Z轴

    // 解析温度数据
    raw_data->temp_raw = sh5001_combine_bytes(buffer[13], buffer[12]);

    raw_data->timestamp = esp_timer_get_time();

    return ESP_OK;
}

static bool sh5001_is_connected(imu_sensor_t *sensor)
{
    if (!sensor || !sensor->priv_data) {
        return false;
    }

    uint8_t device_id = sh5001_get_device_id(sensor);
    return (device_id == SH5001_EXPECTED_ID);
}

static uint8_t sh5001_get_device_id(imu_sensor_t *sensor)
{
    if (!sensor || !sensor->priv_data) {
        return 0;
    }

    sh5001_priv_data_t *priv = (sh5001_priv_data_t*)sensor->priv_data;
    uint8_t device_id = 0;

    esp_err_t ret = sh5001_read_register(priv, SH5001_CHIP_ID, &device_id);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to read device ID");
        return 0;
    }

    return device_id;
}

static esp_err_t sh5001_reset(imu_sensor_t *sensor)
{
    if (!sensor || !sensor->priv_data) {
        return ESP_ERR_INVALID_ARG;
    }

    sh5001_priv_data_t *priv = (sh5001_priv_data_t*)sensor->priv_data;

    // SH5001软件复位
    esp_err_t ret = sh5001_write_register(priv, SH5001_POWER_MODE, 0x01);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to write reset command to SH5001");
        return ret;
    }

    vTaskDelay(pdMS_TO_TICKS(10)); // 等待复位完成
    return ESP_OK;
}

// I2C操作函数实现
static esp_err_t sh5001_write_register(sh5001_priv_data_t *priv, uint8_t reg, uint8_t value)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (priv->device_address << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg, true);
    i2c_master_write_byte(cmd, value, true);
    i2c_master_stop(cmd);

    esp_err_t ret = i2c_master_cmd_begin(priv->i2c_port, cmd, pdMS_TO_TICKS(1000));
    i2c_cmd_link_delete(cmd);

    return ret;
}

static esp_err_t sh5001_read_register(sh5001_priv_data_t *priv, uint8_t reg, uint8_t *value)
{
    if (!value) {
        return ESP_ERR_INVALID_ARG;
    }

    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (priv->device_address << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg, true);
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (priv->device_address << 1) | I2C_MASTER_READ, true);
    i2c_master_read_byte(cmd, value, I2C_MASTER_NACK);
    i2c_master_stop(cmd);

    esp_err_t ret = i2c_master_cmd_begin(priv->i2c_port, cmd, pdMS_TO_TICKS(1000));
    i2c_cmd_link_delete(cmd);

    return ret;
}

static esp_err_t sh5001_read_registers(sh5001_priv_data_t *priv, uint8_t reg, uint8_t *buffer, size_t length)
{
    if (!buffer || length == 0) {
        return ESP_ERR_INVALID_ARG;
    }

    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (priv->device_address << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg, true);
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (priv->device_address << 1) | I2C_MASTER_READ, true);

    if (length > 1) {
        i2c_master_read(cmd, buffer, length - 1, I2C_MASTER_ACK);
    }
    i2c_master_read_byte(cmd, &buffer[length - 1], I2C_MASTER_NACK);
    i2c_master_stop(cmd);

    esp_err_t ret = i2c_master_cmd_begin(priv->i2c_port, cmd, pdMS_TO_TICKS(1000));
    i2c_cmd_link_delete(cmd);

    return ret;
}

static int16_t sh5001_combine_bytes(uint8_t high, uint8_t low)
{
    return (int16_t)((high << 8) | low);
}

// 实现剩余的操作函数
static esp_err_t sh5001_calibrate(imu_sensor_t *sensor, uint32_t calibration_time_ms)
{
    if (!sensor || !sensor->priv_data) {
        return ESP_ERR_INVALID_ARG;
    }

    sh5001_priv_data_t *priv = (sh5001_priv_data_t*)sensor->priv_data;

    if (!priv->connection_verified) {
        ESP_LOGE(TAG, "SH5001 not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Starting SH5001 sensor calibration...");
    ESP_LOGI(TAG, "Please place sensor on a level, stationary surface");

    // 等待传感器稳定
    vTaskDelay(pdMS_TO_TICKS(1000));

    const uint32_t samples = calibration_time_ms / 10; // 每10ms采样一次
    float accel_sum[3] = {0, 0, 0};
    float gyro_sum[3] = {0, 0, 0};
    uint32_t valid_samples = 0;

    for (uint32_t i = 0; i < samples; i++) {
        imu_raw_data_t raw_data;
        esp_err_t ret = sh5001_read_raw_data(sensor, &raw_data);
        if (ret == ESP_OK) {
            accel_sum[0] += raw_data.accel_raw[0] * priv->accel_scale;
            accel_sum[1] += raw_data.accel_raw[1] * priv->accel_scale;
            accel_sum[2] += raw_data.accel_raw[2] * priv->accel_scale;

            gyro_sum[0] += raw_data.gyro_raw[0] * priv->gyro_scale;
            gyro_sum[1] += raw_data.gyro_raw[1] * priv->gyro_scale;
            gyro_sum[2] += raw_data.gyro_raw[2] * priv->gyro_scale;

            valid_samples++;
        }
        vTaskDelay(pdMS_TO_TICKS(10));

        // 显示进度
        if (i % (samples / 10) == 0) {
            ESP_LOGI(TAG, "Calibration progress: %lu%%", (i * 100) / samples);
        }
    }

    if (valid_samples < samples / 2) {
        ESP_LOGE(TAG, "Insufficient valid samples for calibration");
        return ESP_ERR_INVALID_RESPONSE;
    }

    // 计算偏移量
    priv->gyro_offset[0] = gyro_sum[0] / valid_samples;
    priv->gyro_offset[1] = gyro_sum[1] / valid_samples;
    priv->gyro_offset[2] = gyro_sum[2] / valid_samples;

    // 加速度计校准 - 假设Z轴向上为1g
    priv->accel_offset[0] = accel_sum[0] / valid_samples;
    priv->accel_offset[1] = accel_sum[1] / valid_samples;
    priv->accel_offset[2] = (accel_sum[2] / valid_samples) - 1.0f; // 减去重力加速度

    ESP_LOGI(TAG, "Calibration completed!");
    ESP_LOGI(TAG, "Gyro offsets: X=%.3f, Y=%.3f, Z=%.3f (deg/s)",
             priv->gyro_offset[0], priv->gyro_offset[1], priv->gyro_offset[2]);
    ESP_LOGI(TAG, "Accel offsets: X=%.3f, Y=%.3f, Z=%.3f (g)",
             priv->accel_offset[0], priv->accel_offset[1], priv->accel_offset[2]);

    return ESP_OK;
}

static esp_err_t sh5001_set_config(imu_sensor_t *sensor, const imu_config_t *config)
{
    if (!sensor || !config || !sensor->priv_data) {
        return ESP_ERR_INVALID_ARG;
    }

    sh5001_priv_data_t *priv = (sh5001_priv_data_t*)sensor->priv_data;
    sensor->config = *config;

    if (priv->connection_verified) {
        // 重新配置传感器
        esp_err_t ret = sh5001_configure_accelerometer(priv, config);
        if (ret != ESP_OK) return ret;

        ret = sh5001_configure_gyroscope(priv, config);
        if (ret != ESP_OK) return ret;

        sh5001_update_scale_factors(priv, config);
    }

    return ESP_OK;
}

static esp_err_t sh5001_get_config(imu_sensor_t *sensor, imu_config_t *config)
{
    if (!sensor || !config) {
        return ESP_ERR_INVALID_ARG;
    }

    *config = sensor->config;
    return ESP_OK;
}

static const char* sh5001_get_info(imu_sensor_t *sensor)
{
    if (!sensor || !sensor->priv_data) {
        return "Invalid sensor";
    }

    sh5001_priv_data_t *priv = (sh5001_priv_data_t*)sensor->priv_data;

    snprintf(sensor->info_buffer, sizeof(sensor->info_buffer),
             "SH5001 IMU Sensor\n"
             "I2C Port: %d\n"
             "I2C Address: 0x%02X\n"
             "Device ID: 0x%02X\n"
             "Accel Range: ±%dg\n"
             "Gyro Range: ±%d°/s\n"
             "Sample Rate: %dHz\n"
             "Filter: %s\n"
             "Status: %s",
             priv->i2c_port,
             priv->device_address,
             sh5001_get_device_id(sensor),
             sensor->config.accel_range,
             sensor->config.gyro_range,
             sensor->config.sample_rate,
             sensor->config.enable_filter ? "Enabled" : "Disabled",
             sensor->initialized ? "Initialized" : "Not initialized");

    return sensor->info_buffer;
}

static void sh5001_destroy(imu_sensor_t *sensor)
{
    if (!sensor || !sensor->priv_data) {
        return;
    }

    sh5001_priv_data_t *priv = (sh5001_priv_data_t*)sensor->priv_data;

    // 删除I2C驱动
    i2c_driver_delete(priv->i2c_port);

    // 释放私有数据
    free(priv);
    sensor->priv_data = NULL;

    ESP_LOGI(TAG, "SH5001 sensor destroyed");
}
