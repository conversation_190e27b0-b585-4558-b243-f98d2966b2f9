{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/bin/cmake.exe", "cpack": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/bin/cpack.exe", "ctest": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/bin/ctest.exe", "root": "D:/CLion 2024.3/CLion 2024.1.6/bin/cmake/win/x64/share/cmake-3.28"}, "version": {"isDirty": false, "major": 3, "minor": 28, "patch": 6, "string": "3.28.6", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-17cb05228f7139b2e876.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "cache-v2-30a6811f59753af0a252.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-ecc4898121c6212d9828.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-98e95b20fa2e1698f509.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-30a6811f59753af0a252.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-ecc4898121c6212d9828.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-17cb05228f7139b2e876.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, "toolchains-v1": {"jsonFile": "toolchains-v1-98e95b20fa2e1698f509.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}