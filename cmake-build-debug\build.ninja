# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.28

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Arduino_ESP32C3_IMU
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/cmake-build-debug/
# =============================================================================
# Object build statements for EXECUTABLE target Arduino_ESP32C3_IMU


#############################################
# Order-only phony target for Arduino_ESP32C3_IMU

build cmake_object_order_depends_target_Arduino_ESP32C3_IMU: phony || CMakeFiles/Arduino_ESP32C3_IMU.dir

build CMakeFiles/Arduino_ESP32C3_IMU.dir/ESP32C3_IMU_Fusion/IMU_Driver.cpp.obj: CXX_COMPILER__Arduino_ESP32C3_IMU_unscanned_Debug C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion/IMU_Driver.cpp || cmake_object_order_depends_target_Arduino_ESP32C3_IMU
  DEP_FILE = CMakeFiles\Arduino_ESP32C3_IMU.dir\ESP32C3_IMU_Fusion\IMU_Driver.cpp.obj.d
  FLAGS = -g -std=gnu++14 -fdiagnostics-color=always
  INCLUDES = -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src
  OBJECT_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir
  OBJECT_FILE_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir\ESP32C3_IMU_Fusion

build CMakeFiles/Arduino_ESP32C3_IMU.dir/ESP32C3_IMU_Fusion/main.c.obj: C_COMPILER__Arduino_ESP32C3_IMU_unscanned_Debug C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion/main.c || cmake_object_order_depends_target_Arduino_ESP32C3_IMU
  DEP_FILE = CMakeFiles\Arduino_ESP32C3_IMU.dir\ESP32C3_IMU_Fusion\main.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src
  OBJECT_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir
  OBJECT_FILE_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir\ESP32C3_IMU_Fusion

build CMakeFiles/Arduino_ESP32C3_IMU.dir/libraries/Fusion/src/FusionAhrs.c.obj: C_COMPILER__Arduino_ESP32C3_IMU_unscanned_Debug C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src/FusionAhrs.c || cmake_object_order_depends_target_Arduino_ESP32C3_IMU
  DEP_FILE = CMakeFiles\Arduino_ESP32C3_IMU.dir\libraries\Fusion\src\FusionAhrs.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src
  OBJECT_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir
  OBJECT_FILE_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir\libraries\Fusion\src

build CMakeFiles/Arduino_ESP32C3_IMU.dir/libraries/Fusion/src/FusionCompass.c.obj: C_COMPILER__Arduino_ESP32C3_IMU_unscanned_Debug C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src/FusionCompass.c || cmake_object_order_depends_target_Arduino_ESP32C3_IMU
  DEP_FILE = CMakeFiles\Arduino_ESP32C3_IMU.dir\libraries\Fusion\src\FusionCompass.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src
  OBJECT_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir
  OBJECT_FILE_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir\libraries\Fusion\src

build CMakeFiles/Arduino_ESP32C3_IMU.dir/libraries/Fusion/src/FusionOffset.c.obj: C_COMPILER__Arduino_ESP32C3_IMU_unscanned_Debug C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src/FusionOffset.c || cmake_object_order_depends_target_Arduino_ESP32C3_IMU
  DEP_FILE = CMakeFiles\Arduino_ESP32C3_IMU.dir\libraries\Fusion\src\FusionOffset.c.obj.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src
  OBJECT_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir
  OBJECT_FILE_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir\libraries\Fusion\src


# =============================================================================
# Link build statements for EXECUTABLE target Arduino_ESP32C3_IMU


#############################################
# Link the executable Arduino_ESP32C3_IMU.exe

build Arduino_ESP32C3_IMU.exe: CXX_EXECUTABLE_LINKER__Arduino_ESP32C3_IMU_Debug CMakeFiles/Arduino_ESP32C3_IMU.dir/ESP32C3_IMU_Fusion/IMU_Driver.cpp.obj CMakeFiles/Arduino_ESP32C3_IMU.dir/ESP32C3_IMU_Fusion/main.c.obj CMakeFiles/Arduino_ESP32C3_IMU.dir/libraries/Fusion/src/FusionAhrs.c.obj CMakeFiles/Arduino_ESP32C3_IMU.dir/libraries/Fusion/src/FusionCompass.c.obj CMakeFiles/Arduino_ESP32C3_IMU.dir/libraries/Fusion/src/FusionOffset.c.obj
  FLAGS = -g
  LINK_LIBRARIES = -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = Arduino_ESP32C3_IMU.exe
  TARGET_IMPLIB = libArduino_ESP32C3_IMU.dll.a
  TARGET_PDB = Arduino_ESP32C3_IMU.exe.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\Arduino_ESP32C3_IMU\cmake-build-debug && "D:\CLion 2024.3\CLion 2024.1.6\bin\cmake\win\x64\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\Arduino_ESP32C3_IMU\cmake-build-debug && "D:\CLion 2024.3\CLion 2024.1.6\bin\cmake\win\x64\bin\cmake.exe" --regenerate-during-build -SC:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\Arduino_ESP32C3_IMU -BC:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\Arduino_ESP32C3_IMU\cmake-build-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build Arduino_ESP32C3_IMU: phony Arduino_ESP32C3_IMU.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/cmake-build-debug

build all: phony Arduino_ESP32C3_IMU.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/CMakeLists.txt CMakeCache.txt CMakeFiles/3.28.6/CMakeCCompiler.cmake CMakeFiles/3.28.6/CMakeCXXCompiler.cmake CMakeFiles/3.28.6/CMakeRCCompiler.cmake CMakeFiles/3.28.6/CMakeSystem.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCInformation.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCXXInformation.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeGenericSystem.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeRCInformation.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU-C.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-C-ABI.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-C.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-CXX-ABI.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-CXX.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-Initialize.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-windres.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/WindowsPaths.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/CMakeLists.txt CMakeCache.txt CMakeFiles/3.28.6/CMakeCCompiler.cmake CMakeFiles/3.28.6/CMakeCXXCompiler.cmake CMakeFiles/3.28.6/CMakeRCCompiler.cmake CMakeFiles/3.28.6/CMakeSystem.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCInformation.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCXXInformation.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeGenericSystem.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeRCInformation.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU-C.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Compiler/GNU.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-C-ABI.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-C.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-CXX-ABI.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU-CXX.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-GNU.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-Initialize.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows-windres.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/Windows.cmake D$:/CLion$ 2024.3/CLion$ 2024.1.6/bin/cmake/win/x64/share/cmake-3.28/Modules/Platform/WindowsPaths.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
