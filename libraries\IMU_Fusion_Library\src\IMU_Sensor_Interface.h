/**
 * @file IMU_Sensor_Interface.h
 * @brief IMU传感器抽象接口定义
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 定义通用的IMU传感器接口，支持不同型号的IMU传感器
 * 包括SH5001、MPU6050、LSM6DS3等常见IMU传感器
 */

#ifndef IMU_SENSOR_INTERFACE_H
#define IMU_SENSOR_INTERFACE_H

#include <Arduino.h>
#include <stdint.h>

/**
 * @brief IMU传感器数据结构
 */
struct IMU_Data {
    float accel_x;      ///< 加速度计X轴 (g)
    float accel_y;      ///< 加速度计Y轴 (g)
    float accel_z;      ///< 加速度计Z轴 (g)
    float gyro_x;       ///< 陀螺仪X轴 (度/秒)
    float gyro_y;       ///< 陀螺仪Y轴 (度/秒)
    float gyro_z;       ///< 陀螺仪Z轴 (度/秒)
    float temperature;  ///< 温度 (摄氏度)
    uint32_t timestamp; ///< 时间戳 (毫秒)
};

/**
 * @brief IMU传感器原始数据结构
 */
struct IMU_RawData {
    int16_t accel_raw[3];   ///< 加速度计原始数据 [x, y, z]
    int16_t gyro_raw[3];    ///< 陀螺仪原始数据 [x, y, z]
    int16_t temp_raw;       ///< 温度原始数据
    uint32_t timestamp;     ///< 时间戳 (毫秒)
};

/**
 * @brief IMU传感器配置结构
 */
struct IMU_Config {
    uint16_t accel_range;       ///< 加速度计量程 (g)
    uint16_t gyro_range;        ///< 陀螺仪量程 (度/秒)
    uint16_t sample_rate;       ///< 采样率 (Hz)
    bool enable_filter;         ///< 是否启用数字滤波器
    uint8_t filter_bandwidth;   ///< 滤波器带宽
};

/**
 * @brief IMU传感器状态枚举
 */
enum class IMU_Status {
    OK = 0,                 ///< 正常
    ERROR_INIT,             ///< 初始化错误
    ERROR_COMMUNICATION,    ///< 通信错误
    ERROR_INVALID_DATA,     ///< 无效数据
    ERROR_TIMEOUT,          ///< 超时错误
    ERROR_CALIBRATION       ///< 校准错误
};

/**
 * @brief IMU传感器类型枚举
 */
enum class IMU_Type {
    UNKNOWN = 0,
    SH5001,
    MPU6050,
    MPU6500,
    MPU9250,
    LSM6DS3,
    LSM6DSO,
    ICM20602,
    ICM20948
};

/**
 * @brief IMU传感器抽象基类
 * 
 * 定义所有IMU传感器必须实现的接口
 * 支持不同型号的IMU传感器统一管理
 */
class IMU_Sensor_Interface {
public:
    /**
     * @brief 虚析构函数
     */
    virtual ~IMU_Sensor_Interface() = default;

    /**
     * @brief 初始化IMU传感器
     * @param config 传感器配置参数
     * @return IMU_Status 初始化状态
     */
    virtual IMU_Status begin(const IMU_Config& config = {}) = 0;

    /**
     * @brief 读取IMU数据
     * @param data 输出的IMU数据
     * @return IMU_Status 读取状态
     */
    virtual IMU_Status readData(IMU_Data& data) = 0;

    /**
     * @brief 读取IMU原始数据
     * @param raw_data 输出的原始数据
     * @return IMU_Status 读取状态
     */
    virtual IMU_Status readRawData(IMU_RawData& raw_data) = 0;

    /**
     * @brief 检查传感器连接状态
     * @return true 连接正常，false 连接异常
     */
    virtual bool isConnected() = 0;

    /**
     * @brief 获取传感器类型
     * @return IMU_Type 传感器类型
     */
    virtual IMU_Type getType() const = 0;

    /**
     * @brief 获取设备ID
     * @return uint8_t 设备ID
     */
    virtual uint8_t getDeviceID() = 0;

    /**
     * @brief 软件复位传感器
     * @return IMU_Status 复位状态
     */
    virtual IMU_Status reset() = 0;

    /**
     * @brief 校准传感器
     * @param calibration_time 校准时间 (毫秒)
     * @return IMU_Status 校准状态
     */
    virtual IMU_Status calibrate(uint32_t calibration_time = 5000) = 0;

    /**
     * @brief 设置传感器配置
     * @param config 配置参数
     * @return IMU_Status 设置状态
     */
    virtual IMU_Status setConfig(const IMU_Config& config) = 0;

    /**
     * @brief 获取当前配置
     * @param config 输出的配置参数
     * @return IMU_Status 获取状态
     */
    virtual IMU_Status getConfig(IMU_Config& config) = 0;

    /**
     * @brief 获取最后一次错误信息
     * @return const char* 错误信息字符串
     */
    virtual const char* getLastError() = 0;

    /**
     * @brief 获取传感器信息字符串
     * @return const char* 传感器信息
     */
    virtual const char* getInfo() = 0;

protected:
    IMU_Status last_status = IMU_Status::OK;    ///< 最后一次操作状态
    char error_message[128] = {0};              ///< 错误信息缓冲区
    IMU_Config current_config = {};             ///< 当前配置
};

#endif // IMU_SENSOR_INTERFACE_H
