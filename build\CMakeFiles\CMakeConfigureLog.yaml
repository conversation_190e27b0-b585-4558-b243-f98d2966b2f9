
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/Users/<USER>/scoop/apps/mingw-winlibs/current/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/build/CMakeFiles/3.30.2/CompilerIdC/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Users/<USER>/scoop/apps/mingw-winlibs/current/bin/c++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/build/CMakeFiles/3.30.2/CompilerIdCXX/a.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/build/CMakeFiles/CMakeScratch/TryCompile-8cs4io"
      binary: "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/build/CMakeFiles/CMakeScratch/TryCompile-8cs4io"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/build/CMakeFiles/CMakeScratch/TryCompile-8cs4io'
        
        Run Build Command(s): D:/esp/tools/tools/ninja/1.12.1/ninja.exe -v cmTC_9880f
        [1/2] C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\gcc.exe   -v -o CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj -c D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\gcc.exe
        OFFLOAD_TARGET_NAMES=nvptx-none
        Target: x86_64-w64-mingw32
        Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.0 (MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_9880f.dir/'
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/cc1.exe -quiet -v -iprefix C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/ -D_REENTRANT D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_9880f.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=x86-64 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccQtpOtA.s
        GNU C17 (MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2) version 14.2.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 14.2.0, GMP version 6.3.0, MPFR version 4.2.1, MPC version 1.3.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include"
        ignoring nonexistent directory "R:/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc/include"
        ignoring nonexistent directory "/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc/include"
        ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed"
        ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        Compiler executable checksum: 083cdfdb36f80abb23036714270fd518
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_9880f.dir/'
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccQtpOtA.s
        GNU assembler version 2.43.1 (x86_64-w64-mingw32) using BFD version (Binutils for MinGW-W64 x86_64, built by Brecht Sanders, r2) 2.43.1
        COMPILER_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../\x0d
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.'\x0d
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\gcc.exe  -v -Wl,-v CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj -o cmTC_9880f.exe -Wl,--out-implib,libcmTC_9880f.dll.a -Wl,--major-image-version,0,--minor-image-version,0   && cd ."
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\gcc.exe
        COLLECT_LTO_WRAPPER=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe
        OFFLOAD_TARGET_NAMES=nvptx-none
        Target: x86_64-w64-mingw32
        Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.0 (MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2) 
        COMPILER_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_9880f.exe' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_9880f.'
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/collect2.exe -plugin C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8Fwzb1.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_9880f.exe C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0 -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../.. -v CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_9880f.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o
        collect2 version 14.2.0
        C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8Fwzb1.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_9880f.exe C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0 -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../.. -v CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_9880f.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o\x0d
        GNU ld (Binutils for MinGW-W64 x86_64, built by Brecht Sanders, r2) 2.43.1\x0d
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_9880f.exe' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_9880f.'\x0d
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include]
          add: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include]
          add: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed]
          add: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/include]
        collapse include dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/include]
        collapse include dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed]
        collapse include dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/x86_64-w64-mingw32/include]
        implicit include dirs: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/include;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/include;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: 'C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/build/CMakeFiles/CMakeScratch/TryCompile-8cs4io']
        ignore line: []
        ignore line: [Run Build Command(s): D:/esp/tools/tools/ninja/1.12.1/ninja.exe -v cmTC_9880f]
        ignore line: [[1/2] C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\gcc.exe   -v -o CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj -c D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\gcc.exe]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.0 (MinGW-W64 x86_64-ucrt-posix-seh  built by Brecht Sanders  r2) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_9880f.dir/']
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/cc1.exe -quiet -v -iprefix C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/ -D_REENTRANT D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_9880f.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=x86-64 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccQtpOtA.s]
        ignore line: [GNU C17 (MinGW-W64 x86_64-ucrt-posix-seh  built by Brecht Sanders  r2) version 14.2.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 14.2.0  GMP version 6.3.0  MPFR version 4.2.1  MPC version 1.3.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include"]
        ignore line: [ignoring nonexistent directory "R:/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc/include"]
        ignore line: [ignoring nonexistent directory "/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc/include"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include]
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include]
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed]
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: 083cdfdb36f80abb23036714270fd518]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_9880f.dir/']
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccQtpOtA.s]
        ignore line: [GNU assembler version 2.43.1 (x86_64-w64-mingw32) using BFD version (Binutils for MinGW-W64 x86_64  built by Brecht Sanders  r2) 2.43.1]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../\x0d]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.'\x0d]
        ignore line: [[2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\gcc.exe  -v -Wl -v CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj -o cmTC_9880f.exe -Wl --out-implib libcmTC_9880f.dll.a -Wl --major-image-version 0 --minor-image-version 0   && cd ."]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\gcc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.0 (MinGW-W64 x86_64-ucrt-posix-seh  built by Brecht Sanders  r2) ]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_9880f.exe' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_9880f.']
        link line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/collect2.exe -plugin C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8Fwzb1.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_9880f.exe C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0 -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../.. -v CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_9880f.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
          arg [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8Fwzb1.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_9880f.exe] ==> ignore
          arg [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o] ==> obj [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o]
          arg [-LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0] ==> dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0]
          arg [-LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc] ==> dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc]
          arg [-LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib] ==> dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib]
          arg [-LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib] ==> dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../..] ==> dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_9880f.dir/CMakeCCompilerABI.c.obj] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_9880f.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o] ==> obj [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
        linker tool for 'C': Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe
        remove lib [gcc_eh]
        remove lib [msvcrt]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        collapse obj [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o]
        collapse obj [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
        collapse library dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0]
        collapse library dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc]
        collapse library dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib]
        collapse library dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../..] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib]
        implicit libs: [mingw32;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc;mingwex;kernel32]
        implicit objs: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/x86_64-w64-mingw32/lib/crt2.o;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
        implicit dirs: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/x86_64-w64-mingw32/lib;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe" "-v"
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe" "-V"
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe" "--version"
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/build/CMakeFiles/CMakeScratch/TryCompile-vwrkrd"
      binary: "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/build/CMakeFiles/CMakeScratch/TryCompile-vwrkrd"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/build/CMakeFiles/CMakeScratch/TryCompile-vwrkrd'
        
        Run Build Command(s): D:/esp/tools/tools/ninja/1.12.1/ninja.exe -v cmTC_13c0c
        [1/2] C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\c++.exe   -v -o CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj -c D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\c++.exe
        OFFLOAD_TARGET_NAMES=nvptx-none
        Target: x86_64-w64-mingw32
        Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.0 (MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_13c0c.dir/'
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/cc1plus.exe -quiet -v -iprefix C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/ -D_REENTRANT D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_13c0c.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=x86-64 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cclyb6jp.s
        GNU C++17 (MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2) version 14.2.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 14.2.0, GMP version 6.3.0, MPFR version 4.2.1, MPC version 1.3.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0"
        ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/x86_64-w64-mingw32"
        ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/backward"
        ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include"
        ignoring nonexistent directory "R:/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc/include"
        ignoring nonexistent directory "/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc/include"
        ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed"
        ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/x86_64-w64-mingw32
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/backward
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        Compiler executable checksum: 0a151f404c3e06bb380959acc81ec24d
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_13c0c.dir/'
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cclyb6jp.s
        GNU assembler version 2.43.1 (x86_64-w64-mingw32) using BFD version (Binutils for MinGW-W64 x86_64, built by Brecht Sanders, r2) 2.43.1
        COMPILER_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../\x0d
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.'\x0d
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\c++.exe  -v -Wl,-v CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_13c0c.exe -Wl,--out-implib,libcmTC_13c0c.dll.a -Wl,--major-image-version,0,--minor-image-version,0   && cd ."
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\c++.exe
        COLLECT_LTO_WRAPPER=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe
        OFFLOAD_TARGET_NAMES=nvptx-none
        Target: x86_64-w64-mingw32
        Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.0 (MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2) 
        COMPILER_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_13c0c.exe' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_13c0c.'
         C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/collect2.exe -plugin C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccJxoanT.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_13c0c.exe C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0 -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../.. -v CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_13c0c.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o
        collect2 version 14.2.0
        C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccJxoanT.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_13c0c.exe C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0 -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../.. -v CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_13c0c.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o
        GNU ld (Binutils for MinGW-W64 x86_64, built by Brecht Sanders, r2) 2.43.1
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_13c0c.exe' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_13c0c.'\x0d
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0]
          add: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/x86_64-w64-mingw32]
          add: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/backward]
          add: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include]
          add: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include]
          add: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed]
          add: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/include/c++/14.2.0]
        collapse include dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/x86_64-w64-mingw32] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/include/c++/14.2.0/x86_64-w64-mingw32]
        collapse include dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/backward] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/include/c++/14.2.0/backward]
        collapse include dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/include]
        collapse include dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/include]
        collapse include dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed]
        collapse include dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/x86_64-w64-mingw32/include]
        implicit include dirs: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/include/c++/14.2.0;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/include/c++/14.2.0/x86_64-w64-mingw32;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/include/c++/14.2.0/backward;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/include;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/include;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: 'C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/build/CMakeFiles/CMakeScratch/TryCompile-vwrkrd']
        ignore line: []
        ignore line: [Run Build Command(s): D:/esp/tools/tools/ninja/1.12.1/ninja.exe -v cmTC_13c0c]
        ignore line: [[1/2] C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\c++.exe   -v -o CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj -c D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\c++.exe]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.0 (MinGW-W64 x86_64-ucrt-posix-seh  built by Brecht Sanders  r2) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_13c0c.dir/']
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/cc1plus.exe -quiet -v -iprefix C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/ -D_REENTRANT D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_13c0c.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=x86-64 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cclyb6jp.s]
        ignore line: [GNU C++17 (MinGW-W64 x86_64-ucrt-posix-seh  built by Brecht Sanders  r2) version 14.2.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 14.2.0  GMP version 6.3.0  MPFR version 4.2.1  MPC version 1.3.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/x86_64-w64-mingw32"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/backward"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include"]
        ignore line: [ignoring nonexistent directory "R:/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc/include"]
        ignore line: [ignoring nonexistent directory "/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc/include"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed"]
        ignore line: [ignoring duplicate directory "C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0]
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/x86_64-w64-mingw32]
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/backward]
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include]
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include]
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed]
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: 0a151f404c3e06bb380959acc81ec24d]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_13c0c.dir/']
        ignore line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cclyb6jp.s]
        ignore line: [GNU assembler version 2.43.1 (x86_64-w64-mingw32) using BFD version (Binutils for MinGW-W64 x86_64  built by Brecht Sanders  r2) 2.43.1]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../\x0d]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.'\x0d]
        ignore line: [[2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\c++.exe  -v -Wl -v CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_13c0c.exe -Wl --out-implib libcmTC_13c0c.dll.a -Wl --major-image-version 0 --minor-image-version 0   && cd ."]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\scoop\\apps\\mingw-winlibs\\current\\bin\\c++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.0 (MinGW-W64 x86_64-ucrt-posix-seh  built by Brecht Sanders  r2) ]
        ignore line: [COMPILER_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_13c0c.exe' '-shared-libgcc' '-mtune=generic' '-march=x86-64' '-dumpdir' 'cmTC_13c0c.']
        link line: [ C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/collect2.exe -plugin C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll -plugin-opt=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccJxoanT.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_13c0c.exe C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0 -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib -LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../.. -v CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_13c0c.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
          arg [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../libexec/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccJxoanT.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_13c0c.exe] ==> ignore
          arg [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o] ==> obj [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o]
          arg [-LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0] ==> dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0]
          arg [-LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc] ==> dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc]
          arg [-LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib] ==> dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib]
          arg [-LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib] ==> dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LC:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../..] ==> dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_13c0c.dir/CMakeCXXCompilerABI.cpp.obj] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_13c0c.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o] ==> obj [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
        linker tool for 'CXX': Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o]
        collapse obj [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
        collapse library dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0]
        collapse library dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc]
        collapse library dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib]
        collapse library dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/x86_64-w64-mingw32/lib]
        collapse library dir [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../..] ==> [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib]
        implicit libs: [stdc++;mingw32;gcc_s;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc_s;gcc;mingwex;kernel32]
        implicit objs: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/x86_64-w64-mingw32/lib/crt2.o;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
        implicit dirs: [C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc/x86_64-w64-mingw32/14.2.0;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib/gcc;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/x86_64-w64-mingw32/lib;C:/Users/<USER>/scoop/apps/mingw-winlibs/14.2.0-12.0.0-r2/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe" "-v"
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe" "-V"
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "D:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "Configured with: ../configure --prefix=/R/winlibs_staging_ucrt64/inst_gcc-14.2.0/share/gcc --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --enable-offload-targets=nvptx-none --with-pkgversion='MinGW-W64 x86_64-ucrt-posix-seh, built by Brecht Sanders, r2' --with-tune=generic --enable-checking=release --enable-threads=posix --disable-sjlj-exceptions --disable-libunwind-exceptions --disable-serial-configure --disable-bootstrap --enable-host-shared --enable-plugin --disable-default-ssp --disable-rpath --disable-libstdcxx-debug --disable-version-specific-runtime-libs --with-stabs --disable-symvers --enable-languages=c,c++,fortran,lto,objc,obj-c++ --disable-gold --disable-nls --disable-stage1-checking --disable-win32-registry --disable-multilib --enable-ld --enable-libquadmath --enable-libada --enable-libssp --enable-libstdcxx --enable-lto --enable-fully-dynamic-string --enable-libgomp --enable-graphite --enable-mingw-wildcard --enable-libstdcxx-time --enable-libstdcxx-pch --with-mpc=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-mpfr=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-gmp=/c/Prog/winlibs_staging_ucrt/custombuilt64 --with-isl=/c/Prog/winlibs_staging_ucrt/custombuilt64 --disable-libstdcxx-backtrace --enable-install-libiberty --enable-__cxa_atexit --without-included-gettext --with-diagnostics-color=auto --enable-clocale=generic --with-libiconv --with-system-zlib --with-build-sysroot=/R/winlibs_staging_ucrt64/gcc-14.2.0/build_mingw/mingw-w64 CFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -I/c/Prog/winlibs_staging_ucrt/custombuilt64/include/libdl-win32   -march=nocona -msahf -mtune=generic -O2 -Wno-error=format' CXXFLAGS='-D__USE_MINGW_ANSI_STDIO=0 -Wno-int-conversion  -march=nocona -msahf -mtune=generic -O2' LDFLAGS='-pthread -Wl,--no-insert-timestamp -Wl,--dynamicbase -Wl,--high-entropy-va -Wl,--nxcompat -Wl,--tsaware' LD=/c/Prog/winlibs_staging_ucrt/custombuilt64/share/binutils/bin/ld.exe" "--version"
      
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
...
