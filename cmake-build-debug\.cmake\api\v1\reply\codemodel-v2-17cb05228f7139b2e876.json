{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.28"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Arduino_ESP32C3_IMU", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "Arduino_ESP32C3_IMU::@6890427a1f51a3e7e1df", "jsonFile": "target-A<PERSON>uino_ESP32C3_IMU-Debug-48311b628df9ab58c765.json", "name": "Arduino_ESP32C3_IMU", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/cmake-build-debug", "source": "C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU"}, "version": {"major": 2, "minor": 6}}