/**
 * @file Compare_Output_Test.ino
 * @brief 对比新库与原代码的输出
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 这个程序同时运行新库和原有代码逻辑，对比输出结果
 * 验证新库的兼容性和准确性
 */

#include <Wire.h>
#include "Fusion.h"
#include <IMU_Fusion_Library.h>

// 原有代码的全局变量
FusionAhrs ahrs_original;

// 新库实例
IMU_Fusion_Library imu_fusion;

// 共享的IMU数据
float shared_accel_x, shared_accel_y, shared_accel_z;
float shared_gyro_x, shared_gyro_y, shared_gyro_z;

// 配置参数
#define SAMPLE_PERIOD_MS    10
#define SAMPLE_PERIOD_S     (SAMPLE_PERIOD_MS / 1000.0f)
#define SDA_PIN 8
#define SCL_PIN 9
#define COMPARISON_SAMPLES  1000  // 对比1000个样本

// 测试变量
unsigned long last_sample_time = 0;
uint32_t sample_count = 0;
uint32_t match_count = 0;
float max_difference = 0.0f;
float total_difference = 0.0f;

void setup() {
    Serial.begin(115200);
    while (!Serial) {
        delay(10);
    }
    
    Serial.println("========================================");
    Serial.println("新库与原代码输出对比测试");
    Serial.println("========================================");
    
    // 初始化I2C
    Wire.begin(SDA_PIN, SCL_PIN);
    Wire.setClock(400000);
    
    // 初始化原有代码的Fusion算法
    initializeOriginalFusion();
    
    // 初始化新库
    IMU_Config sensor_config = createDefaultSensorConfig(16, 2000, 125);
    Fusion_Config fusion_config = createDefaultFusionConfig(100.0f);
    
    // 使用相同的配置参数
    fusion_config.convention = FusionConventionNwu;
    fusion_config.gain = 0.5f;
    fusion_config.gyroscope_range = 2000.0f;
    fusion_config.acceleration_rejection = 10.0f;
    fusion_config.magnetic_rejection = 10.0f;
    fusion_config.recovery_trigger_period = 5 * (1000 / SAMPLE_PERIOD_MS);
    fusion_config.sample_period = SAMPLE_PERIOD_S;
    fusion_config.use_magnetometer = false;
    
    if (imu_fusion.autoDetectAndInit(SDA_PIN, SCL_PIN, 400000) != IMU_Status::OK) {
        Serial.println("❌ 新库初始化失败");
        while (1) delay(1000);
    }
    
    if (imu_fusion.begin(sensor_config, fusion_config) != IMU_Status::OK) {
        Serial.println("❌ 新库配置失败");
        while (1) delay(1000);
    }
    
    Serial.println("✅ 初始化完成");
    Serial.println("开始对比测试...");
    Serial.println("格式: [样本] 原代码四元数 | 新库四元数 | 差异");
    Serial.println("========================================");
    
    last_sample_time = millis();
}

void loop() {
    unsigned long current_time = millis();
    
    if (current_time - last_sample_time >= SAMPLE_PERIOD_MS && sample_count < COMPARISON_SAMPLES) {
        last_sample_time = current_time;
        
        // 读取共享的IMU数据
        if (readSharedIMUData()) {
            // 处理原有代码逻辑
            FusionQuaternion quat_original = processOriginalFusion();
            
            // 处理新库逻辑
            Quaternion_Data quat_new;
            if (imu_fusion.updateManual(shared_accel_x, shared_accel_y, shared_accel_z,
                                       shared_gyro_x, shared_gyro_y, shared_gyro_z) == IMU_Status::OK) {
                imu_fusion.getQuaternion(quat_new);
            }
            
            // 对比结果
            compareQuaternions(quat_original, quat_new);
            
            sample_count++;
            
            // 每100个样本输出一次详细对比
            if (sample_count % 100 == 0) {
                printDetailedComparison(quat_original, quat_new);
            }
        }
    }
    
    // 测试完成
    if (sample_count >= COMPARISON_SAMPLES) {
        printFinalResults();
        while (1) {
            delay(1000);
        }
    }
    
    delay(1);
}

void initializeOriginalFusion() {
    // 初始化AHRS算法 (与原代码相同)
    FusionAhrsInitialise(&ahrs_original);
    
    // 设置AHRS算法参数 (与原代码相同)
    const FusionAhrsSettings settings = {
        .convention = FusionConventionNwu,
        .gain = 0.5f,
        .gyroscopeRange = 2000.0f,
        .accelerationRejection = 10.0f,
        .magneticRejection = 10.0f,
        .recoveryTriggerPeriod = 5 * (1000 / SAMPLE_PERIOD_MS),
    };
    
    FusionAhrsSetSettings(&ahrs_original, &settings);
}

bool readSharedIMUData() {
    // 从新库读取原始传感器数据
    IMU_Data data;
    if (imu_fusion.getRawData(data) == IMU_Status::OK) {
        shared_accel_x = data.accel_x;
        shared_accel_y = data.accel_y;
        shared_accel_z = data.accel_z;
        shared_gyro_x = data.gyro_x;
        shared_gyro_y = data.gyro_y;
        shared_gyro_z = data.gyro_z;
        return true;
    }
    return false;
}

FusionQuaternion processOriginalFusion() {
    // 准备Fusion库所需的数据格式 (与原代码相同)
    const FusionVector gyroscope = {
        .axis = {shared_gyro_x, shared_gyro_y, shared_gyro_z}
    };
    const FusionVector accelerometer = {
        .axis = {shared_accel_x, shared_accel_y, shared_accel_z}
    };
    
    // 更新AHRS算法 (与原代码相同)
    FusionAhrsUpdateNoMagnetometer(&ahrs_original, gyroscope, accelerometer, SAMPLE_PERIOD_S);
    
    // 获取四元数 (与原代码相同)
    return FusionAhrsGetQuaternion(&ahrs_original);
}

void compareQuaternions(const FusionQuaternion& quat_orig, const Quaternion_Data& quat_new) {
    // 计算四元数差异
    float diff_w = abs(quat_orig.element.w - quat_new.w);
    float diff_x = abs(quat_orig.element.x - quat_new.x);
    float diff_y = abs(quat_orig.element.y - quat_new.y);
    float diff_z = abs(quat_orig.element.z - quat_new.z);
    
    float total_diff = diff_w + diff_x + diff_y + diff_z;
    total_difference += total_diff;
    
    if (total_diff > max_difference) {
        max_difference = total_diff;
    }
    
    // 如果差异很小，认为匹配
    if (total_diff < 0.001f) {
        match_count++;
    }
}

void printDetailedComparison(const FusionQuaternion& quat_orig, const Quaternion_Data& quat_new) {
    Serial.printf("[%04lu] 原代码: %.4f,%.4f,%.4f,%.4f | 新库: %.4f,%.4f,%.4f,%.4f | 差异: %.6f\n",
                  sample_count,
                  quat_orig.element.w, quat_orig.element.x, quat_orig.element.y, quat_orig.element.z,
                  quat_new.w, quat_new.x, quat_new.y, quat_new.z,
                  abs(quat_orig.element.w - quat_new.w) + 
                  abs(quat_orig.element.x - quat_new.x) + 
                  abs(quat_orig.element.y - quat_new.y) + 
                  abs(quat_orig.element.z - quat_new.z));
}

void printFinalResults() {
    Serial.println("\n========================================");
    Serial.println("对比测试完成!");
    Serial.println("========================================");
    
    float match_rate = ((float)match_count / sample_count) * 100.0f;
    float avg_difference = total_difference / sample_count;
    
    Serial.printf("总样本数: %lu\n", sample_count);
    Serial.printf("匹配样本数: %lu\n", match_count);
    Serial.printf("匹配率: %.2f%%\n", match_rate);
    Serial.printf("平均差异: %.6f\n", avg_difference);
    Serial.printf("最大差异: %.6f\n", max_difference);
    
    Serial.println("\n评估结果:");
    if (match_rate > 95.0f && avg_difference < 0.001f) {
        Serial.println("🎉 优秀! 新库与原代码输出高度一致");
    } else if (match_rate > 90.0f && avg_difference < 0.01f) {
        Serial.println("✅ 良好! 新库与原代码输出基本一致");
    } else if (match_rate > 80.0f && avg_difference < 0.1f) {
        Serial.println("⚠️  可接受! 新库与原代码有轻微差异");
    } else {
        Serial.println("❌ 需要改进! 新库与原代码差异较大");
    }
    
    Serial.println("\n差异分析:");
    if (avg_difference < 0.0001f) {
        Serial.println("差异极小，可能由于浮点精度造成");
    } else if (avg_difference < 0.001f) {
        Serial.println("差异很小，在可接受范围内");
    } else if (avg_difference < 0.01f) {
        Serial.println("差异较小，可能由于算法实现细节不同");
    } else {
        Serial.println("差异较大，需要检查算法实现");
    }
    
    Serial.println("========================================");
}
