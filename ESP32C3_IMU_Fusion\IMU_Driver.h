/**
 * @file IMU_Driver.h
 * @brief IMU传感器驱动头文件 (Arduino版本) - SH5001
 * <AUTHOR> IMU Fusion Project
 */

#ifndef IMU_DRIVER_H
#define IMU_DRIVER_H

#include <Arduino.h>
#include <Wire.h>
#include <cstdint>

// SH5001寄存器地址
#define SH5001_ADDR             0x37    // SH5001 I2C地址 (SDO=VDD)
#define SH5001_ADDR_ALT         0x36    // SH5001 I2C地址 (SDO=GND)
#define SH5001_CHIP_ID          0x1F    // 设备ID寄存器
#define SH5001_ACC_XL           0x00    // 加速度计X轴低字节
#define SH5001_ACC_XH           0x01    // 加速度计X轴高字节
#define SH5001_ACC_YL           0x02    // 加速度计Y轴低字节
#define SH5001_ACC_YH           0x03    // 加速度计Y轴高字节
#define SH5001_ACC_ZL           0x04    // 加速度计Z轴低字节
#define SH5001_ACC_ZH           0x05    // 加速度计Z轴高字节
#define SH5001_GYRO_XL          0x06    // 陀螺仪X轴低字节
#define SH5001_GYRO_XH          0x07    // 陀螺仪X轴高字节
#define SH5001_GYRO_YL          0x08    // 陀螺仪Y轴低字节
#define SH5001_GYRO_YH          0x09    // 陀螺仪Y轴高字节
#define SH5001_GYRO_ZL          0x0A    // 陀螺仪Z轴低字节
#define SH5001_GYRO_ZH          0x0B    // 陀螺仪Z轴高字节
#define SH5001_TEMP_ZL          0x0C    // 温度低字节
#define SH5001_TEMP_ZH          0x0D    // 温度高字节

// 配置寄存器
#define SH5001_ACC_CONF0        0x20    // 加速度计配置寄存器0
#define SH5001_ACC_CONF1        0x21    // 加速度计配置寄存器1
#define SH5001_ACC_CONF2        0x22    // 加速度计配置寄存器2
#define SH5001_GYRO_CONF0       0x23    // 陀螺仪配置寄存器0
#define SH5001_GYRO_CONF1       0x24    // 陀螺仪配置寄存器1
#define SH5001_GYRO_CONF2       0x25    // 陀螺仪配置寄存器2
#define SH5001_TEMP_CONF0       0x28    // 温度配置寄存器0
#define SH5001_POWER_MODE       0x30    // 电源模式寄存器

// 量程配置 - 陀螺仪
#define GYRO_RANGE_250DPS       0x30    // ±250°/s
#define GYRO_RANGE_500DPS       0x40    // ±500°/s
#define GYRO_RANGE_1000DPS      0x50    // ±1000°/s
#define GYRO_RANGE_2000DPS      0x60    // ±2000°/s

// 量程配置 - 加速度计
#define ACCEL_RANGE_4G          0x10    // ±4g
#define ACCEL_RANGE_8G          0x20    // ±8g
#define ACCEL_RANGE_16G         0x30    // ±16g

// SH5001配置参数
#define SH5001_ACC_ODR_125HZ    0x03    // 加速度计125Hz输出数据率
#define SH5001_ACC_RANGE_16G    0x30    // ±16G量程
#define SH5001_ACC_ODRX040      0x00    // ODR × 0.40 截止频率
#define SH5001_ACC_FILTER_EN    0x01    // 使能数字滤波器
#define SH5001_ACC_BYPASS_EN    0x02    // 使能旁路模式

#define SH5001_GYRO_ODR_125HZ   0x03    // 陀螺仪125Hz输出数据率
#define SH5001_GYRO_RANGE_2000  0x60    // ±2000°/s量程
#define SH5001_GYRO_ODRX040     0x00    // ODR × 0.40 截止频率
#define SH5001_GYRO_FILTER_EN   0x01    // 使能数字滤波器
#define SH5001_GYRO_BYPASS_EN   0x02    // 使能旁路模式

#define SH5001_TEMP_ODR_63HZ    0x06    // 温度传感器63Hz输出数据率
#define SH5001_TEMP_EN          0x01    // 使能温度传感器

/**
 * @brief IMU驱动类
 */
class IMU_Driver {
private:
    uint8_t deviceAddress;
    float gyroScale;    // 陀螺仪量程转换因子
    float accelScale;   // 加速度计量程转换因子

    // 私有方法
    bool writeRegister(uint8_t reg, uint8_t value);
    uint8_t readRegister(uint8_t reg);
    bool readRegisters(uint8_t reg, uint8_t* buffer, uint8_t length);
    int16_t combineBytes(uint8_t high, uint8_t low);

    // SH5001特有的私有方法
    void softReset();
    void configAccelerometer();
    void configGyroscope();
    void configTemperature();

public:
    /**
     * @brief 构造函数
     * @param addr I2C设备地址 (默认0x37)
     */
    IMU_Driver(uint8_t addr = SH5001_ADDR);
    
    /**
     * @brief 初始化IMU传感器
     * @return true 成功，false 失败
     */
    bool begin();
    
    /**
     * @brief 读取IMU数据
     * @param accel_x 加速度计X轴输出 (g)
     * @param accel_y 加速度计Y轴输出 (g)
     * @param accel_z 加速度计Z轴输出 (g)
     * @param gyro_x 陀螺仪X轴输出 (度/秒)
     * @param gyro_y 陀螺仪Y轴输出 (度/秒)
     * @param gyro_z 陀螺仪Z轴输出 (度/秒)
     * @return true 成功，false 失败
     */
    bool readData(float& accel_x, float& accel_y, float& accel_z,
                  float& gyro_x, float& gyro_y, float& gyro_z);
    
    /**
     * @brief 读取原始数据
     * @param accel_raw 加速度计原始数据数组 [x, y, z]
     * @param gyro_raw 陀螺仪原始数据数组 [x, y, z]
     * @return true 成功，false 失败
     */
    bool readRawData(int16_t accel_raw[3], int16_t gyro_raw[3]);
    
    /**
     * @brief 检查设备连接
     * @return true 连接正常，false 连接异常
     */
    bool testConnection();
    
    /**
     * @brief 设置陀螺仪量程
     * @param range 量程设置 (GYRO_RANGE_250DPS, GYRO_RANGE_500DPS, 等)
     * @return true 成功，false 失败
     */
    bool setGyroRange(uint8_t range);

    /**
     * @brief 设置加速度计量程
     * @param range 量程设置 (ACCEL_RANGE_4G, ACCEL_RANGE_8G, 等)
     * @return true 成功，false 失败
     */
    bool setAccelRange(uint8_t range);

    /**
     * @brief 获取设备ID
     * @return 设备ID (正常应为0xA1)
     */
    uint8_t getDeviceID();

    /**
     * @brief 读取温度数据
     * @return 温度值 (摄氏度)
     */
    float getTemperature();
};

#endif // IMU_DRIVER_H
