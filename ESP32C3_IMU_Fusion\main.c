//
// Created by y<PERSON><PERSON><PERSON> on 2025/8/11.
//
#include <stdio.h>
#include <synchapi.h>
#include "Fusion.h"
#include "IMU_Driver.h"

// 日志标签
const char* TAG = "IMU_FUSION";

// IMU数据采样周期 (100Hz = 10ms)
#define SAMPLE_PERIOD_MS    10
#define SAMPLE_PERIOD_S     (SAMPLE_PERIOD_MS / 1000.0f)

// I2C引脚定义 (ESP32-C3)
#define SDA_PIN 8
#define SCL_PIN 9

// 全局变量
FusionAhrs ahrs;
IMU_Driver imu;
unsigned long lastSampleTime = 0;

//函数声明
void initializeFusion();
void processSensorData(float accel_x, float accel_y, float accel_z,
                       float gyro_x, float gyro_y, float gyro_z);
void printResults();

int main(){
    // 初始化串口
    printf("========================================");
    printf("ESP32-C3 IMU Fusion Arduino项目启动");
    printf("========================================");

    // 初始化I2C
    //    Wire.begin(SDA_PIN, SCL_PIN);
    //    Wire.setClock(400000); // 400kHz

    printf("I2C总线初始化完成");

    // 初始化IMU传感器
    if (!imu.begin()) {
        printf("错误: IMU初始化失败!");
        printf("请检查:");
        printf("1. SH5001连接是否正确");
        printf("2. I2C引脚配置 (SDA=GPIO8, SCL=GPIO9)");
        printf("3. 传感器供电是否正常");
        printf("4. I2C地址设置 (SDO=VDD时为0x37, SDO=GND时为0x36)");
        Sleep(1000);
    }

    printf("IMU传感器初始化成功");

    // 初始化Fusion AHRS算法
    initializeFusion();

    printf("Fusion AHRS算法初始化完成");
    printf("系统准备就绪，开始数据采集...");
    printf("========================================");

    lastSampleTime = millis();

    //主循环
    while (1){
        unsigned long currentTime = millis();

        // 控制采样频率 (100Hz)
        if (currentTime - lastSampleTime >= SAMPLE_PERIOD_MS) {
            lastSampleTime = currentTime;

            // 读取IMU数据
            float accel_x, accel_y, accel_z;
            float gyro_x, gyro_y, gyro_z;

            if (imu.readData(accel_x, accel_y, accel_z, gyro_x, gyro_y, gyro_z)) {
                // 处理传感器数据
                processSensorData(accel_x, accel_y, accel_z, gyro_x, gyro_y, gyro_z);

                // 每10ms输出一次结果 (100Hz输出频率，与原SH5001示例保持一致)
                printResults();
            } else {
                printf("警告: 读取IMU数据失败");
            }
        }

        // 短暂延时，避免占用过多CPU
//        delay(1);
    }
}

/**
 * @brief 初始化Fusion AHRS算法
 */
void initializeFusion() {
    // 初始化AHRS算法
    FusionAhrsInitialise(&ahrs);

    // 设置AHRS算法参数
    const FusionAhrsSettings settings = {
            .convention = FusionConventionNwu,          // 北-西-上坐标系
            .gain = 0.5f,                               // 融合增益
            .gyroscopeRange = 2000.0f,                  // 陀螺仪量程 (度/秒)
            .accelerationRejection = 10.0f,             // 加速度拒绝阈值 (度)
            .magneticRejection = 10.0f,                 // 磁场拒绝阈值 (度)
            .recoveryTriggerPeriod = 5 * (1000 / SAMPLE_PERIOD_MS), // 恢复触发周期 (5秒)
    };

    FusionAhrsSetSettings(&ahrs, &settings);
}

/**
 * @brief 处理传感器数据
 */
void processSensorData(float accel_x, float accel_y, float accel_z,
                       float gyro_x, float gyro_y, float gyro_z) {
    // 准备Fusion库所需的数据格式
    const FusionVector gyroscope = {
            .axis = {gyro_x, gyro_y, gyro_z}
    };
    const FusionVector accelerometer = {
            .axis = {accel_x, accel_y, accel_z}
    };
    // 更新AHRS算法 (仅使用陀螺仪和加速度计)
    FusionAhrsUpdateNoMagnetometer(&ahrs, gyroscope, accelerometer, SAMPLE_PERIOD_S);
}

/**
 * @brief 打印结果
 */
void printResults() {
    // 获取四元数
    const FusionQuaternion quaternion = FusionAhrsGetQuaternion(&ahrs);

    // 输出四元数格式，与原SH5001示例保持一致
    printf("@f",quaternion.element.w, 4);
    printf(",");
    printf("@f",quaternion.element.x, 4);
    printf(",");
    printf("@f",quaternion.element.y, 4);
    printf(",");
    printf("@f",quaternion.element.z, 4);
    printf("\n");
}
