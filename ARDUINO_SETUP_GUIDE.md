# 🔧 Arduino IDE 设置指南

## 📋 系统要求

- **操作系统**: Windows 10+, macOS 10.14+, 或 Linux
- **Arduino IDE**: 2.0+ (推荐) 或 1.8.19+
- **可用存储空间**: 至少 2GB

## 🚀 步骤1: 安装Arduino IDE

### Windows用户
1. 访问 [Arduino官网](https://www.arduino.cc/en/software)
2. 下载"Arduino IDE 2.x.x for Windows"
3. 运行安装程序，按默认设置安装
4. 启动Arduino IDE

### macOS用户
1. 访问 [Arduino官网](https://www.arduino.cc/en/software)
2. 下载"Arduino IDE 2.x.x for macOS"
3. 将Arduino IDE拖拽到Applications文件夹
4. 启动Arduino IDE

### Linux用户
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install arduino

# 或下载AppImage版本
wget https://downloads.arduino.cc/arduino-ide/arduino-ide_2.x.x_Linux_64bit.AppImage
chmod +x arduino-ide_2.x.x_Linux_64bit.AppImage
./arduino-ide_2.x.x_Linux_64bit.AppImage
```

## 🔌 步骤2: 安装ESP32开发板包

### 2.1 添加开发板管理器URL
1. 打开Arduino IDE
2. 进入 **文件** → **首选项** (Windows/Linux) 或 **Arduino IDE** → **首选项** (macOS)
3. 在"附加开发板管理器网址"字段中添加：
   ```
   https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json
   ```
4. 如果已有其他URL，用逗号分隔
5. 点击"确定"

### 2.2 安装ESP32开发板包
1. 进入 **工具** → **开发板** → **开发板管理器**
2. 在搜索框中输入"ESP32"
3. 找到"esp32 by Espressif Systems"
4. 点击"安装"按钮
5. 等待安装完成（可能需要几分钟）

### 2.3 验证安装
1. 进入 **工具** → **开发板**
2. 应该能看到"ESP32 Arduino"选项
3. 展开后应该能看到"ESP32C3 Dev Module"

## 📦 步骤3: 安装项目

### 3.1 方法A: 手动安装库文件

1. **安装Fusion库**:
   - 找到Arduino库目录：
     - **Windows**: `C:\Users\<USER>\Documents\Arduino\libraries\`
     - **macOS**: `~/Documents/Arduino/libraries/`
     - **Linux**: `~/Arduino/libraries/`
   - 将`Arduino_ESP32C3_IMU/libraries/Fusion`文件夹复制到上述目录
   - 重启Arduino IDE

2. **打开项目**:
   - 将`Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion`文件夹复制到Arduino项目目录
   - 在Arduino IDE中打开`ESP32C3_IMU_Fusion.ino`

### 3.2 方法B: 直接使用项目文件夹

1. 在Arduino IDE中选择 **文件** → **打开**
2. 导航到`Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion/`文件夹
3. 选择`ESP32C3_IMU_Fusion.ino`文件
4. Arduino IDE会自动加载整个项目

## ⚙️ 步骤4: 配置开发板

### 4.1 选择开发板
1. 进入 **工具** → **开发板** → **ESP32 Arduino** → **ESP32C3 Dev Module**

### 4.2 配置开发板参数
设置以下参数（在**工具**菜单中）：

| 参数 | 推荐值 | 说明 |
|------|--------|------|
| Upload Speed | 921600 | 上传速度 |
| CPU Frequency | 160MHz (WiFi/BT) | CPU频率 |
| Flash Frequency | 80MHz | Flash频率 |
| Flash Mode | QIO | Flash模式 |
| Flash Size | 4MB (32Mb) | Flash大小 |
| Partition Scheme | Default 4MB with spiffs | 分区方案 |
| Core Debug Level | None | 调试级别 |

### 4.3 选择串口
1. 连接ESP32-C3开发板到电脑
2. 进入 **工具** → **端口**
3. 选择对应的串口：
   - **Windows**: COM3, COM4等
   - **macOS**: /dev/cu.usbserial-xxx
   - **Linux**: /dev/ttyUSB0, /dev/ttyACM0等

## 🔍 步骤5: 验证安装

### 5.1 编译测试
1. 在Arduino IDE中打开项目
2. 点击"验证"按钮 (✓)
3. 等待编译完成
4. 检查是否有错误信息

### 5.2 上传测试
1. 确保ESP32-C3已连接
2. 点击"上传"按钮 (→)
3. 等待上传完成
4. 打开串口监视器查看输出

## 🐛 常见问题解决

### 问题1: 找不到ESP32开发板
**现象**: 工具菜单中没有ESP32选项
**解决**:
1. 检查开发板管理器URL是否正确添加
2. 重新安装ESP32开发板包
3. 重启Arduino IDE

### 问题2: 编译错误 "Fusion.h not found"
**现象**: 编译时提示找不到Fusion.h文件
**解决**:
1. 确保Fusion库已正确复制到libraries文件夹
2. 重启Arduino IDE
3. 检查库文件夹结构是否正确

### 问题3: 上传失败
**现象**: 上传时出现"Failed to connect"错误
**解决**:
1. 检查USB线连接
2. 确认选择了正确的串口
3. 按住ESP32-C3的BOOT按钮，然后点击上传
4. 尝试降低上传速度到115200

### 问题4: 串口监视器无输出
**现象**: 程序上传成功但串口监视器无输出
**解决**:
1. 检查串口监视器波特率设置为115200
2. 按ESP32-C3的RESET按钮重启设备
3. 检查串口是否被其他程序占用

### 问题5: 权限错误 (Linux/macOS)
**现象**: 无法访问串口设备
**解决**:
```bash
# Linux
sudo usermod -a -G dialout $USER
sudo chmod 666 /dev/ttyUSB0

# macOS
sudo dseditgroup -o edit -a $USER -t user wheel
```

## 📚 有用的资源

- [Arduino IDE官方文档](https://docs.arduino.cc/software/ide-v2)
- [ESP32 Arduino核心文档](https://docs.espressif.com/projects/arduino-esp32/en/latest/)
- [ESP32-C3数据手册](https://www.espressif.com/sites/default/files/documentation/esp32-c3_datasheet_en.pdf)
- [MPU6050数据手册](https://invensense.tdk.com/wp-content/uploads/2015/02/MPU-6000-Datasheet1.pdf)

## 🎯 下一步

安装完成后，请参考主README.md文件了解：
- 硬件连接方法
- 项目配置选项
- API使用说明
- 故障排除指南

---

**安装愉快！** 🎉
