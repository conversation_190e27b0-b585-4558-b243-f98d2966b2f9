/**
 * @file imu_sensor_interface.c
 * @brief IMU传感器抽象接口实现 (纯C实现)
 * <AUTHOR> IMU Fusion Project
 * @version 2.0
 * @date 2025-01-02
 */

#include "imu_sensor_interface.h"
#include "sh5001_sensor.h"
#include <stdlib.h>
#include <string.h>
#include "esp_log.h"

static const char *TAG = "imu_sensor";

imu_sensor_t* imu_sensor_create(imu_type_t type, void *priv_data)
{
    imu_sensor_t *sensor = calloc(1, sizeof(imu_sensor_t));
    if (!sensor) {
        ESP_LOGE(TAG, "Failed to allocate memory for sensor");
        return NULL;
    }

    sensor->type = type;
    sensor->priv_data = priv_data;
    sensor->initialized = false;

    // 根据传感器类型设置操作函数指针
    switch (type) {
        case IMU_TYPE_SH5001:
            sensor->ops = sh5001_get_ops();
            break;
        
        case IMU_TYPE_MPU6050:
            // TODO: 实现MPU6050操作函数
            ESP_LOGW(TAG, "MPU6050 not implemented yet");
            free(sensor);
            return NULL;
            
        case IMU_TYPE_LSM6DS3:
            // TODO: 实现LSM6DS3操作函数
            ESP_LOGW(TAG, "LSM6DS3 not implemented yet");
            free(sensor);
            return NULL;
            
        default:
            ESP_LOGE(TAG, "Unsupported sensor type: %d", type);
            free(sensor);
            return NULL;
    }

    if (!sensor->ops) {
        ESP_LOGE(TAG, "Failed to get operations for sensor type: %d", type);
        free(sensor);
        return NULL;
    }

    ESP_LOGI(TAG, "Created sensor instance for type: %d", type);
    return sensor;
}

void imu_sensor_destroy(imu_sensor_t *sensor)
{
    if (!sensor) {
        return;
    }

    if (sensor->ops && sensor->ops->destroy) {
        sensor->ops->destroy(sensor);
    }

    free(sensor);
    ESP_LOGI(TAG, "Destroyed sensor instance");
}

esp_err_t imu_sensor_init(imu_sensor_t *sensor, const imu_config_t *config)
{
    if (!sensor || !sensor->ops || !sensor->ops->init) {
        ESP_LOGE(TAG, "Invalid sensor or operations");
        return ESP_ERR_INVALID_ARG;
    }

    // 使用默认配置如果没有提供
    imu_config_t default_config;
    if (!config) {
        default_config = imu_sensor_create_default_config(16, 2000, 125);
        config = &default_config;
    }

    esp_err_t ret = sensor->ops->init(sensor, config);
    if (ret == ESP_OK) {
        sensor->config = *config;
        sensor->initialized = true;
        ESP_LOGI(TAG, "Sensor initialized successfully");
    } else {
        ESP_LOGE(TAG, "Failed to initialize sensor: %s", esp_err_to_name(ret));
    }

    return ret;
}

esp_err_t imu_sensor_read_data(imu_sensor_t *sensor, imu_data_t *data)
{
    if (!sensor || !data) {
        return ESP_ERR_INVALID_ARG;
    }

    if (!sensor->initialized) {
        ESP_LOGE(TAG, "Sensor not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (!sensor->ops || !sensor->ops->read_data) {
        ESP_LOGE(TAG, "Read data operation not supported");
        return ESP_ERR_NOT_SUPPORTED;
    }

    return sensor->ops->read_data(sensor, data);
}

esp_err_t imu_sensor_read_raw_data(imu_sensor_t *sensor, imu_raw_data_t *raw_data)
{
    if (!sensor || !raw_data) {
        return ESP_ERR_INVALID_ARG;
    }

    if (!sensor->initialized) {
        ESP_LOGE(TAG, "Sensor not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (!sensor->ops || !sensor->ops->read_raw_data) {
        ESP_LOGE(TAG, "Read raw data operation not supported");
        return ESP_ERR_NOT_SUPPORTED;
    }

    return sensor->ops->read_raw_data(sensor, raw_data);
}

bool imu_sensor_is_connected(imu_sensor_t *sensor)
{
    if (!sensor || !sensor->ops || !sensor->ops->is_connected) {
        return false;
    }

    return sensor->ops->is_connected(sensor);
}

imu_type_t imu_sensor_get_type(imu_sensor_t *sensor)
{
    if (!sensor) {
        return IMU_TYPE_UNKNOWN;
    }

    return sensor->type;
}

uint8_t imu_sensor_get_device_id(imu_sensor_t *sensor)
{
    if (!sensor || !sensor->ops || !sensor->ops->get_device_id) {
        return 0;
    }

    return sensor->ops->get_device_id(sensor);
}

esp_err_t imu_sensor_reset(imu_sensor_t *sensor)
{
    if (!sensor || !sensor->ops || !sensor->ops->reset) {
        return ESP_ERR_INVALID_ARG;
    }

    esp_err_t ret = sensor->ops->reset(sensor);
    if (ret == ESP_OK) {
        sensor->initialized = false;
        ESP_LOGI(TAG, "Sensor reset successfully");
    }

    return ret;
}

esp_err_t imu_sensor_calibrate(imu_sensor_t *sensor, uint32_t calibration_time_ms)
{
    if (!sensor || !sensor->ops || !sensor->ops->calibrate) {
        return ESP_ERR_INVALID_ARG;
    }

    if (!sensor->initialized) {
        ESP_LOGE(TAG, "Sensor not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Starting sensor calibration for %lu ms", calibration_time_ms);
    return sensor->ops->calibrate(sensor, calibration_time_ms);
}

esp_err_t imu_sensor_set_config(imu_sensor_t *sensor, const imu_config_t *config)
{
    if (!sensor || !config) {
        return ESP_ERR_INVALID_ARG;
    }

    if (!sensor->ops || !sensor->ops->set_config) {
        return ESP_ERR_NOT_SUPPORTED;
    }

    esp_err_t ret = sensor->ops->set_config(sensor, config);
    if (ret == ESP_OK) {
        sensor->config = *config;
        ESP_LOGI(TAG, "Sensor configuration updated");
    }

    return ret;
}

esp_err_t imu_sensor_get_config(imu_sensor_t *sensor, imu_config_t *config)
{
    if (!sensor || !config) {
        return ESP_ERR_INVALID_ARG;
    }

    if (!sensor->ops || !sensor->ops->get_config) {
        // 返回缓存的配置
        *config = sensor->config;
        return ESP_OK;
    }

    return sensor->ops->get_config(sensor, config);
}

const char* imu_sensor_get_info(imu_sensor_t *sensor)
{
    if (!sensor) {
        return "Invalid sensor";
    }

    if (!sensor->ops || !sensor->ops->get_info) {
        snprintf(sensor->info_buffer, sizeof(sensor->info_buffer),
                 "Sensor Type: %d\nStatus: %s",
                 sensor->type,
                 sensor->initialized ? "Initialized" : "Not initialized");
        return sensor->info_buffer;
    }

    return sensor->ops->get_info(sensor);
}

imu_config_t imu_sensor_create_default_config(uint16_t accel_range, uint16_t gyro_range, uint16_t sample_rate)
{
    imu_config_t config = {
        .accel_range = accel_range,
        .gyro_range = gyro_range,
        .sample_rate = sample_rate,
        .enable_filter = true,
        .filter_bandwidth = 40
    };

    return config;
}
