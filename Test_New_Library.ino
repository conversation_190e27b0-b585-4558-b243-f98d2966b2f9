/**
 * @file Test_New_Library.ino
 * @brief 测试新的IMU融合库
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 这个测试程序验证新的IMU融合库是否能正确工作
 * 并与原有代码输出进行对比
 */

#include <IMU_Fusion_Library.h>

// 创建IMU融合库实例
IMU_Fusion_Library imu_fusion;

// 测试配置
#define SAMPLE_PERIOD_MS    10      // 100Hz采样 (与原代码相同)
#define TEST_DURATION_MS    30000   // 测试30秒
#define PRINT_INTERVAL_MS   100     // 每100ms输出一次 (10Hz)

// I2C引脚定义 (与原代码相同)
#define SDA_PIN 8
#define SCL_PIN 9

// 测试变量
unsigned long test_start_time = 0;
unsigned long last_update_time = 0;
unsigned long last_print_time = 0;
uint32_t sample_count = 0;
uint32_t error_count = 0;

// 数据验证
float last_quaternion[4] = {1.0f, 0.0f, 0.0f, 0.0f};
bool first_sample = true;

void setup() {
    Serial.begin(115200);
    while (!Serial) {
        delay(10);
    }
    
    Serial.println("========================================");
    Serial.println("IMU融合库测试程序");
    Serial.println("========================================");
    
    // 创建与原代码完全相同的配置
    IMU_Config sensor_config;
    sensor_config.accel_range = 16;         // ±16G
    sensor_config.gyro_range = 2000;        // ±2000°/s
    sensor_config.sample_rate = 125;        // 125Hz
    sensor_config.enable_filter = true;     // 启用数字滤波器
    sensor_config.filter_bandwidth = 40;    // ODR × 0.40 截止频率
    
    Fusion_Config fusion_config;
    fusion_config.convention = FusionConventionNwu;          // 北-西-上坐标系
    fusion_config.gain = 0.5f;                               // 融合增益
    fusion_config.gyroscope_range = 2000.0f;                 // 陀螺仪量程
    fusion_config.acceleration_rejection = 10.0f;            // 加速度拒绝阈值
    fusion_config.magnetic_rejection = 10.0f;                // 磁场拒绝阈值
    fusion_config.recovery_trigger_period = 5 * (1000 / SAMPLE_PERIOD_MS); // 恢复触发周期
    fusion_config.sample_period = SAMPLE_PERIOD_MS / 1000.0f; // 采样周期
    fusion_config.use_magnetometer = false;                  // 不使用磁力计
    
    Serial.println("正在初始化IMU融合库...");
    
    // 自动检测并初始化
    IMU_Status status = imu_fusion.autoDetectAndInit(SDA_PIN, SCL_PIN, 400000);
    
    if (status != IMU_Status::OK) {
        Serial.println("❌ 错误: IMU传感器初始化失败!");
        Serial.printf("错误信息: %s\n", imu_fusion.getLastError());
        while (1) {
            delay(1000);
        }
    }
    
    Serial.println("✅ IMU传感器初始化成功");
    
    // 初始化融合库
    status = imu_fusion.begin(sensor_config, fusion_config);
    
    if (status != IMU_Status::OK) {
        Serial.println("❌ 错误: 融合库初始化失败!");
        Serial.printf("错误信息: %s\n", imu_fusion.getLastError());
        while (1) {
            delay(1000);
        }
    }
    
    Serial.println("✅ 融合库初始化成功");
    
    // 显示传感器信息
    Serial.println("\n传感器信息:");
    Serial.println(imu_fusion.getSensorInfo());
    
    // 显示库信息
    Serial.println("\n库信息:");
    Serial.println(imu_fusion.getLibraryInfo());
    
    Serial.println("\n开始测试...");
    Serial.println("测试时长: 30秒");
    Serial.println("输出格式: w,x,y,z (四元数) - 与原代码相同");
    Serial.println("========================================");
    
    test_start_time = millis();
    last_update_time = test_start_time;
    last_print_time = test_start_time;
}

void loop() {
    unsigned long current_time = millis();
    unsigned long elapsed_time = current_time - test_start_time;
    
    // 检查测试是否完成
    if (elapsed_time >= TEST_DURATION_MS) {
        printTestResults();
        while (1) {
            delay(1000);
        }
    }
    
    // 控制采样频率 (100Hz)
    if (current_time - last_update_time >= SAMPLE_PERIOD_MS) {
        last_update_time = current_time;
        
        // 更新IMU数据并执行融合
        IMU_Status status = imu_fusion.update();
        
        if (status == IMU_Status::OK) {
            sample_count++;
            
            // 验证数据有效性
            if (!validateQuaternionData()) {
                error_count++;
            }
        } else {
            error_count++;
            Serial.printf("⚠️  数据更新失败: %s\n", imu_fusion.getLastError());
        }
    }
    
    // 控制输出频率 (10Hz)
    if (current_time - last_print_time >= PRINT_INTERVAL_MS) {
        last_print_time = current_time;
        
        // 输出四元数 (与原代码格式相同)
        imu_fusion.printQuaternion(4);
        
        // 每5秒显示一次进度
        if ((elapsed_time / 5000) * 5000 == elapsed_time && elapsed_time > 0) {
            Serial.printf("# 测试进度: %lu/%d秒, 采样数: %lu, 错误数: %lu\n", 
                         elapsed_time / 1000, TEST_DURATION_MS / 1000, sample_count, error_count);
        }
    }
    
    delay(1);
}

bool validateQuaternionData() {
    float w, x, y, z;
    
    if (imu_fusion.getQuaternion(w, x, y, z) != IMU_Status::OK) {
        return false;
    }
    
    // 检查四元数模长是否接近1
    float magnitude = sqrt(w*w + x*x + y*y + z*z);
    if (magnitude < 0.9f || magnitude > 1.1f) {
        Serial.printf("⚠️  四元数模长异常: %.3f\n", magnitude);
        return false;
    }
    
    // 检查是否有NaN或无穷大
    if (isnan(w) || isnan(x) || isnan(y) || isnan(z) ||
        isinf(w) || isinf(x) || isinf(y) || isinf(z)) {
        Serial.println("⚠️  四元数包含NaN或无穷大");
        return false;
    }
    
    // 检查变化是否过于剧烈 (除了第一个样本)
    if (!first_sample) {
        float change = sqrt(pow(w - last_quaternion[0], 2) + 
                           pow(x - last_quaternion[1], 2) + 
                           pow(y - last_quaternion[2], 2) + 
                           pow(z - last_quaternion[3], 2));
        
        if (change > 0.5f) { // 如果变化过大，可能有问题
            Serial.printf("⚠️  四元数变化过大: %.3f\n", change);
            return false;
        }
    }
    
    // 保存当前值用于下次比较
    last_quaternion[0] = w;
    last_quaternion[1] = x;
    last_quaternion[2] = y;
    last_quaternion[3] = z;
    first_sample = false;
    
    return true;
}

void printTestResults() {
    Serial.println("\n========================================");
    Serial.println("测试完成!");
    Serial.println("========================================");
    
    // 获取统计信息
    uint32_t total_samples, failed_samples, uptime;
    float success_rate;
    imu_fusion.getStatistics(total_samples, failed_samples, success_rate, uptime);
    
    Serial.printf("测试时长: %lu 秒\n", uptime / 1000);
    Serial.printf("总采样数: %lu\n", sample_count);
    Serial.printf("错误采样数: %lu\n", error_count);
    Serial.printf("数据成功率: %.2f%%\n", ((float)(sample_count - error_count) / sample_count) * 100.0f);
    Serial.printf("库统计成功率: %.2f%%\n", success_rate);
    Serial.printf("平均采样率: %.1f Hz\n", (float)sample_count / (uptime / 1000.0f));
    Serial.printf("目标采样率: %d Hz\n", 1000 / SAMPLE_PERIOD_MS);
    
    // 测试其他功能
    Serial.println("\n功能测试:");
    
    // 测试欧拉角输出
    float roll, pitch, yaw;
    if (imu_fusion.getEulerAngles(roll, pitch, yaw) == IMU_Status::OK) {
        Serial.printf("✅ 欧拉角输出: Roll=%.2f°, Pitch=%.2f°, Yaw=%.2f°\n", roll, pitch, yaw);
    } else {
        Serial.println("❌ 欧拉角输出失败");
    }
    
    // 测试航向角
    float heading = imu_fusion.getHeading();
    Serial.printf("✅ 航向角: %.2f°\n", heading);
    
    // 测试原始数据
    IMU_Data raw_data;
    if (imu_fusion.getRawData(raw_data) == IMU_Status::OK) {
        Serial.printf("✅ 原始数据: Accel(%.3f,%.3f,%.3f)g, Gyro(%.1f,%.1f,%.1f)°/s, Temp=%.1f°C\n",
                     raw_data.accel_x, raw_data.accel_y, raw_data.accel_z,
                     raw_data.gyro_x, raw_data.gyro_y, raw_data.gyro_z,
                     raw_data.temperature);
    } else {
        Serial.println("❌ 原始数据读取失败");
    }
    
    // 测试传感器连接状态
    if (imu_fusion.isSensorConnected()) {
        Serial.println("✅ 传感器连接正常");
    } else {
        Serial.println("❌ 传感器连接异常");
    }
    
    // 总体评估
    Serial.println("\n总体评估:");
    if (success_rate > 95.0f && error_count < sample_count * 0.05f) {
        Serial.println("🎉 测试通过! 新库工作正常");
    } else if (success_rate > 90.0f) {
        Serial.println("⚠️  测试基本通过，但有一些问题需要注意");
    } else {
        Serial.println("❌ 测试失败，需要检查问题");
    }
    
    Serial.println("========================================");
}
