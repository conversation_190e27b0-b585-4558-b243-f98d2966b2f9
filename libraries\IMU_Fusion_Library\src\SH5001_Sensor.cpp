/**
 * @file SH5001_Sensor.cpp
 * @brief SH5001 IMU传感器驱动适配器实现文件
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 */

#include "SH5001_Sensor.h"
#include <math.h>
#include <string.h>

SH5001_Sensor::SH5001_Sensor(uint8_t addr) 
    : device_address(addr)
    , gyro_scale(1.0f)
    , accel_scale(1.0f)
    , connection_verified(false)
{
    // 初始化偏移量
    memset(accel_offset, 0, sizeof(accel_offset));
    memset(gyro_offset, 0, sizeof(gyro_offset));
    
    // 设置默认配置
    current_config.accel_range = 16;
    current_config.gyro_range = 2000;
    current_config.sample_rate = 125;
    current_config.enable_filter = true;
    current_config.filter_bandwidth = 40;
    
    last_status = IMU_Status::OK;
    strcpy(error_message, "SH5001 sensor created");
}

SH5001_Sensor::~SH5001_Sensor() {
    // 析构函数 - 无需特殊清理
}

IMU_Status SH5001_Sensor::begin(const IMU_Config& config) {
    // 更新配置
    if (config.accel_range > 0) current_config.accel_range = config.accel_range;
    if (config.gyro_range > 0) current_config.gyro_range = config.gyro_range;
    if (config.sample_rate > 0) current_config.sample_rate = config.sample_rate;
    current_config.enable_filter = config.enable_filter;
    current_config.filter_bandwidth = config.filter_bandwidth;

    // 检查设备连接
    if (!isConnected()) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "SH5001 device not found on I2C bus");
        return last_status;
    }

    // 软件复位
    IMU_Status status = softReset();
    if (status != IMU_Status::OK) {
        return status;
    }
    
    delay(50); // 等待复位完成

    // 配置传感器
    status = configureAccelerometer();
    if (status != IMU_Status::OK) {
        return status;
    }

    status = configureGyroscope();
    if (status != IMU_Status::OK) {
        return status;
    }

    status = configureTemperature();
    if (status != IMU_Status::OK) {
        return status;
    }

    // 更新量程转换因子
    updateScaleFactors();

    connection_verified = true;
    last_status = IMU_Status::OK;
    strcpy(error_message, "SH5001 initialized successfully");
    
    return IMU_Status::OK;
}

IMU_Status SH5001_Sensor::readData(IMU_Data& data) {
    if (!connection_verified) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "SH5001 not initialized");
        return last_status;
    }

    // 读取原始数据
    IMU_RawData raw_data;
    IMU_Status status = readRawData(raw_data);
    if (status != IMU_Status::OK) {
        return status;
    }

    // 转换为物理单位
    data.accel_x = (raw_data.accel_raw[0] * accel_scale) - accel_offset[0];
    data.accel_y = (raw_data.accel_raw[1] * accel_scale) - accel_offset[1];
    data.accel_z = (raw_data.accel_raw[2] * accel_scale) - accel_offset[2];
    
    data.gyro_x = (raw_data.gyro_raw[0] * gyro_scale) - gyro_offset[0];
    data.gyro_y = (raw_data.gyro_raw[1] * gyro_scale) - gyro_offset[1];
    data.gyro_z = (raw_data.gyro_raw[2] * gyro_scale) - gyro_offset[2];
    
    data.temperature = raw_data.temp_raw * 0.0625f; // SH5001温度转换因子
    data.timestamp = millis();

    last_status = IMU_Status::OK;
    return IMU_Status::OK;
}

IMU_Status SH5001_Sensor::readRawData(IMU_RawData& raw_data) {
    if (!connection_verified) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "SH5001 not initialized");
        return last_status;
    }

    uint8_t buffer[14]; // 6字节加速度 + 6字节陀螺仪 + 2字节温度
    
    // 读取所有传感器数据
    if (!readRegisters(SH5001_ACC_XL, buffer, 14)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to read sensor data from SH5001");
        return last_status;
    }

    // 解析加速度计数据
    raw_data.accel_raw[0] = combineBytes(buffer[1], buffer[0]);  // X轴
    raw_data.accel_raw[1] = combineBytes(buffer[3], buffer[2]);  // Y轴
    raw_data.accel_raw[2] = combineBytes(buffer[5], buffer[4]);  // Z轴

    // 解析陀螺仪数据
    raw_data.gyro_raw[0] = combineBytes(buffer[7], buffer[6]);   // X轴
    raw_data.gyro_raw[1] = combineBytes(buffer[9], buffer[8]);   // Y轴
    raw_data.gyro_raw[2] = combineBytes(buffer[11], buffer[10]); // Z轴

    // 解析温度数据
    raw_data.temp_raw = combineBytes(buffer[13], buffer[12]);

    raw_data.timestamp = millis();

    last_status = IMU_Status::OK;
    return IMU_Status::OK;
}

bool SH5001_Sensor::isConnected() {
    uint8_t device_id = getDeviceID();
    return (device_id == SH5001_EXPECTED_ID);
}

IMU_Type SH5001_Sensor::getType() const {
    return IMU_Type::SH5001;
}

uint8_t SH5001_Sensor::getDeviceID() {
    return readRegister(SH5001_CHIP_ID);
}

IMU_Status SH5001_Sensor::reset() {
    return softReset();
}

// 私有方法实现
bool SH5001_Sensor::writeRegister(uint8_t reg, uint8_t value) {
    Wire.beginTransmission(device_address);
    Wire.write(reg);
    Wire.write(value);
    return (Wire.endTransmission() == 0);
}

uint8_t SH5001_Sensor::readRegister(uint8_t reg) {
    Wire.beginTransmission(device_address);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) {
        return 0;
    }
    
    Wire.requestFrom(device_address, (uint8_t)1);
    if (Wire.available()) {
        return Wire.read();
    }
    return 0;
}

bool SH5001_Sensor::readRegisters(uint8_t reg, uint8_t* buffer, uint8_t length) {
    Wire.beginTransmission(device_address);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) {
        return false;
    }
    
    Wire.requestFrom(device_address, length);
    uint8_t count = 0;
    while (Wire.available() && count < length) {
        buffer[count++] = Wire.read();
    }
    
    return (count == length);
}

int16_t SH5001_Sensor::combineBytes(uint8_t high, uint8_t low) {
    return (int16_t)((high << 8) | low);
}

IMU_Status SH5001_Sensor::softReset() {
    // SH5001软件复位实现
    if (!writeRegister(SH5001_POWER_MODE, 0x01)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to write reset command to SH5001");
        return last_status;
    }
    
    delay(10); // 等待复位完成
    return IMU_Status::OK;
}

void SH5001_Sensor::updateScaleFactors() {
    // 根据配置的量程计算转换因子
    switch (current_config.accel_range) {
        case 4:  accel_scale = 4.0f / 32768.0f; break;
        case 8:  accel_scale = 8.0f / 32768.0f; break;
        case 16: accel_scale = 16.0f / 32768.0f; break;
        default: accel_scale = 16.0f / 32768.0f; break;
    }

    switch (current_config.gyro_range) {
        case 250:  gyro_scale = 250.0f / 32768.0f; break;
        case 500:  gyro_scale = 500.0f / 32768.0f; break;
        case 1000: gyro_scale = 1000.0f / 32768.0f; break;
        case 2000: gyro_scale = 2000.0f / 32768.0f; break;
        default:   gyro_scale = 2000.0f / 32768.0f; break;
    }
}

IMU_Status SH5001_Sensor::configureAccelerometer() {
    uint8_t accel_range_reg = mapAccelRange(current_config.accel_range);
    uint8_t sample_rate_reg = mapSampleRate(current_config.sample_rate);

    // 配置加速度计量程和输出数据率
    if (!writeRegister(SH5001_ACC_CONF0, sample_rate_reg)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to configure accelerometer ODR");
        return last_status;
    }

    if (!writeRegister(SH5001_ACC_CONF1, accel_range_reg)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to configure accelerometer range");
        return last_status;
    }

    // 配置滤波器
    uint8_t filter_config = current_config.enable_filter ? 0x01 : 0x00;
    if (!writeRegister(SH5001_ACC_CONF2, filter_config)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to configure accelerometer filter");
        return last_status;
    }

    return IMU_Status::OK;
}

IMU_Status SH5001_Sensor::configureGyroscope() {
    uint8_t gyro_range_reg = mapGyroRange(current_config.gyro_range);
    uint8_t sample_rate_reg = mapSampleRate(current_config.sample_rate);

    // 配置陀螺仪量程和输出数据率
    if (!writeRegister(SH5001_GYRO_CONF0, sample_rate_reg)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to configure gyroscope ODR");
        return last_status;
    }

    if (!writeRegister(SH5001_GYRO_CONF1, gyro_range_reg)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to configure gyroscope range");
        return last_status;
    }

    // 配置滤波器
    uint8_t filter_config = current_config.enable_filter ? 0x01 : 0x00;
    if (!writeRegister(SH5001_GYRO_CONF2, filter_config)) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to configure gyroscope filter");
        return last_status;
    }

    return IMU_Status::OK;
}

IMU_Status SH5001_Sensor::configureTemperature() {
    // 配置温度传感器
    if (!writeRegister(SH5001_TEMP_CONF0, 0x07)) { // 启用温度传感器，63Hz
        last_status = IMU_Status::ERROR_COMMUNICATION;
        strcpy(error_message, "Failed to configure temperature sensor");
        return last_status;
    }

    return IMU_Status::OK;
}

uint8_t SH5001_Sensor::mapAccelRange(uint16_t range) {
    switch (range) {
        case 4:  return SH5001_ACCEL_RANGE_4G;
        case 8:  return SH5001_ACCEL_RANGE_8G;
        case 16: return SH5001_ACCEL_RANGE_16G;
        default: return SH5001_ACCEL_RANGE_16G;
    }
}

uint8_t SH5001_Sensor::mapGyroRange(uint16_t range) {
    switch (range) {
        case 250:  return SH5001_GYRO_RANGE_250;
        case 500:  return SH5001_GYRO_RANGE_500;
        case 1000: return SH5001_GYRO_RANGE_1000;
        case 2000: return SH5001_GYRO_RANGE_2000;
        default:   return SH5001_GYRO_RANGE_2000;
    }
}

uint8_t SH5001_Sensor::mapSampleRate(uint16_t rate) {
    if (rate >= 1000) return 0x06;      // 1000Hz
    else if (rate >= 500) return 0x05;  // 500Hz
    else if (rate >= 250) return 0x04;  // 250Hz
    else return 0x03;                   // 125Hz
}

IMU_Status SH5001_Sensor::calibrate(uint32_t calibration_time) {
    if (!connection_verified) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "SH5001 not initialized");
        return last_status;
    }

    Serial.println("开始校准SH5001传感器...");
    Serial.println("请将传感器放置在水平静止位置");

    // 等待传感器稳定
    delay(1000);

    const uint32_t samples = calibration_time / 10; // 每10ms采样一次
    float accel_sum[3] = {0, 0, 0};
    float gyro_sum[3] = {0, 0, 0};
    uint32_t valid_samples = 0;

    for (uint32_t i = 0; i < samples; i++) {
        IMU_RawData raw_data;
        if (readRawData(raw_data) == IMU_Status::OK) {
            accel_sum[0] += raw_data.accel_raw[0] * accel_scale;
            accel_sum[1] += raw_data.accel_raw[1] * accel_scale;
            accel_sum[2] += raw_data.accel_raw[2] * accel_scale;

            gyro_sum[0] += raw_data.gyro_raw[0] * gyro_scale;
            gyro_sum[1] += raw_data.gyro_raw[1] * gyro_scale;
            gyro_sum[2] += raw_data.gyro_raw[2] * gyro_scale;

            valid_samples++;
        }
        delay(10);

        // 显示进度
        if (i % (samples / 10) == 0) {
            Serial.print("校准进度: ");
            Serial.print((i * 100) / samples);
            Serial.println("%");
        }
    }

    if (valid_samples < samples / 2) {
        last_status = IMU_Status::ERROR_CALIBRATION;
        strcpy(error_message, "Insufficient valid samples for calibration");
        return last_status;
    }

    // 计算偏移量
    gyro_offset[0] = gyro_sum[0] / valid_samples;
    gyro_offset[1] = gyro_sum[1] / valid_samples;
    gyro_offset[2] = gyro_sum[2] / valid_samples;

    // 加速度计校准 - 假设Z轴向上为1g
    accel_offset[0] = accel_sum[0] / valid_samples;
    accel_offset[1] = accel_sum[1] / valid_samples;
    accel_offset[2] = (accel_sum[2] / valid_samples) - 1.0f; // 减去重力加速度

    Serial.println("校准完成!");
    Serial.printf("陀螺仪偏移: X=%.3f, Y=%.3f, Z=%.3f (度/秒)\n",
                  gyro_offset[0], gyro_offset[1], gyro_offset[2]);
    Serial.printf("加速度计偏移: X=%.3f, Y=%.3f, Z=%.3f (g)\n",
                  accel_offset[0], accel_offset[1], accel_offset[2]);

    last_status = IMU_Status::OK;
    strcpy(error_message, "Calibration completed successfully");
    return IMU_Status::OK;
}

IMU_Status SH5001_Sensor::setConfig(const IMU_Config& config) {
    current_config = config;

    if (connection_verified) {
        // 重新配置传感器
        IMU_Status status = configureAccelerometer();
        if (status != IMU_Status::OK) return status;

        status = configureGyroscope();
        if (status != IMU_Status::OK) return status;

        updateScaleFactors();
    }

    return IMU_Status::OK;
}

IMU_Status SH5001_Sensor::getConfig(IMU_Config& config) {
    config = current_config;
    return IMU_Status::OK;
}

const char* SH5001_Sensor::getLastError() {
    return error_message;
}

const char* SH5001_Sensor::getInfo() {
    static char info_buffer[256];
    snprintf(info_buffer, sizeof(info_buffer),
             "SH5001 IMU Sensor\n"
             "I2C Address: 0x%02X\n"
             "Device ID: 0x%02X\n"
             "Accel Range: ±%dg\n"
             "Gyro Range: ±%d°/s\n"
             "Sample Rate: %dHz\n"
             "Filter: %s",
             device_address,
             getDeviceID(),
             current_config.accel_range,
             current_config.gyro_range,
             current_config.sample_rate,
             current_config.enable_filter ? "Enabled" : "Disabled");
    return info_buffer;
}

IMU_Status SH5001_Sensor::setI2CAddress(uint8_t addr) {
    device_address = addr;
    connection_verified = false; // 需要重新验证连接
    return IMU_Status::OK;
}

float SH5001_Sensor::getTemperature() {
    uint8_t temp_low = readRegister(SH5001_TEMP_ZL);
    uint8_t temp_high = readRegister(SH5001_TEMP_ZH);
    int16_t temp_raw = combineBytes(temp_high, temp_low);
    return temp_raw * 0.0625f; // SH5001温度转换因子
}

IMU_Status SH5001_Sensor::selfTest() {
    if (!connection_verified) {
        last_status = IMU_Status::ERROR_INIT;
        strcpy(error_message, "SH5001 not initialized");
        return last_status;
    }

    // 读取设备ID验证
    uint8_t device_id = getDeviceID();
    if (device_id != SH5001_EXPECTED_ID) {
        last_status = IMU_Status::ERROR_COMMUNICATION;
        snprintf(error_message, sizeof(error_message),
                "Device ID mismatch: expected 0x%02X, got 0x%02X",
                SH5001_EXPECTED_ID, device_id);
        return last_status;
    }

    // 读取几个样本验证数据有效性
    for (int i = 0; i < 5; i++) {
        IMU_Data data;
        IMU_Status status = readData(data);
        if (status != IMU_Status::OK) {
            last_status = IMU_Status::ERROR_COMMUNICATION;
            strcpy(error_message, "Failed to read valid data during self-test");
            return last_status;
        }

        // 检查数据合理性
        float accel_mag = sqrt(data.accel_x * data.accel_x +
                              data.accel_y * data.accel_y +
                              data.accel_z * data.accel_z);

        if (accel_mag < 0.5f || accel_mag > 2.0f) {
            last_status = IMU_Status::ERROR_INVALID_DATA;
            strcpy(error_message, "Accelerometer data out of reasonable range");
            return last_status;
        }

        delay(10);
    }

    last_status = IMU_Status::OK;
    strcpy(error_message, "Self-test passed");
    return IMU_Status::OK;
}

void SH5001_Sensor::getCalibrationOffsets(float accel_offsets[3], float gyro_offsets[3]) {
    memcpy(accel_offsets, accel_offset, sizeof(accel_offset));
    memcpy(gyro_offsets, gyro_offset, sizeof(gyro_offset));
}

IMU_Status SH5001_Sensor::setCalibrationOffsets(const float accel_offsets[3], const float gyro_offsets[3]) {
    memcpy(accel_offset, accel_offsets, sizeof(accel_offset));
    memcpy(gyro_offset, gyro_offsets, sizeof(gyro_offset));

    last_status = IMU_Status::OK;
    strcpy(error_message, "Calibration offsets updated");
    return IMU_Status::OK;
}
