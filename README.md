# 🚀 ESP32-C3 IMU Fusion Arduino项目

## 📋 项目简介

这是一个基于ESP32-C3和MPU6050的IMU数据融合Arduino项目，使用Fusion库进行高精度姿态估计和传感器数据融合。

### ✨ 主要特性

- 🎯 **高精度姿态估计**: 使用Madgwick AHRS算法
- 🔄 **实时数据融合**: 陀螺仪 + 加速度计数据融合
- 📊 **线性加速度计算**: 自动去除重力影响
- 🛡️ **加速度拒绝**: 自动检测并处理外部加速度干扰
- 🔧 **Arduino兼容**: 完全兼容Arduino IDE和框架
- 📱 **ESP32-C3优化**: 专为ESP32-C3微控制器优化

## 📁 项目结构

```
Arduino_ESP32C3_IMU/
├── ESP32C3_IMU_Fusion/           # 主项目文件夹
│   ├── ESP32C3_IMU_Fusion.ino   # Arduino主程序
│   ├── IMU_Driver.h              # IMU驱动头文件
│   └── IMU_Driver.cpp            # IMU驱动实现
├── libraries/                    # Arduino库文件夹
│   └── Fusion/                   # Fusion传感器融合库
│       ├── library.properties    # 库属性文件
│       └── src/                  # 库源代码
│           ├── Fusion.h          # 主头文件
│           ├── FusionAhrs.h/.c   # AHRS算法
│           ├── FusionMath.h      # 数学函数
│           └── ...               # 其他库文件
└── README.md                     # 本文档
```

## 🔧 硬件要求

### 必需硬件
- ✅ **ESP32-C3开发板** (如ESP32-C3-DevKitM-1)
- ✅ **MPU6050 IMU传感器模块**
- ✅ **杜邦线** 若干
- ✅ **USB-C数据线**

### 硬件连接

```
ESP32-C3 开发板    MPU6050 模块
--------------    ------------
3.3V          ->  VCC
GND           ->  GND  
GPIO4 (SDA)   ->  SDA
GPIO5 (SCL)   ->  SCL
```

**注意**: GPIO4和GPIO5是ESP32-C3推荐的I2C引脚，具有内置上拉电阻。

## 💻 软件要求

### Arduino IDE设置
1. **Arduino IDE 2.0+** 或 **Arduino IDE 1.8.19+**
2. **ESP32开发板包** v2.0.0+

### 安装ESP32开发板包
1. 打开Arduino IDE
2. 进入 **文件** → **首选项**
3. 在"附加开发板管理器网址"中添加：
   ```
   https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json
   ```
4. 进入 **工具** → **开发板** → **开发板管理器**
5. 搜索"ESP32"并安装"esp32 by Espressif Systems"

## 🚀 快速开始

### 1. 安装项目

#### 方法A: 复制到Arduino库文件夹
1. 将`libraries/Fusion`文件夹复制到Arduino库目录：
   - **Windows**: `C:\Users\<USER>\Documents\Arduino\libraries\`
   - **macOS**: `~/Documents/Arduino/libraries/`
   - **Linux**: `~/Arduino/libraries/`

2. 将`ESP32C3_IMU_Fusion`文件夹复制到Arduino项目目录：
   - **Windows**: `C:\Users\<USER>\Documents\Arduino\`

#### 方法B: 直接使用项目文件夹
1. 在Arduino IDE中打开`ESP32C3_IMU_Fusion.ino`文件
2. Arduino IDE会自动识别项目结构

### 2. 配置Arduino IDE

1. **选择开发板**:
   - 进入 **工具** → **开发板** → **ESP32 Arduino** → **ESP32C3 Dev Module**

2. **配置开发板参数**:
   ```
   开发板: "ESP32C3 Dev Module"
   Upload Speed: "921600"
   CPU Frequency: "160MHz (WiFi/BT)"
   Flash Frequency: "80MHz"
   Flash Mode: "QIO"
   Flash Size: "4MB (32Mb)"
   Partition Scheme: "Default 4MB with spiffs"
   Core Debug Level: "None"
   ```

3. **选择串口**:
   - 连接ESP32-C3到电脑
   - 进入 **工具** → **端口** → 选择对应的COM端口

### 3. 编译和上传

1. **编译项目**: 点击"验证"按钮 (✓)
2. **上传程序**: 点击"上传"按钮 (→)
3. **监控串口**: 点击"串口监视器"按钮

### 4. 查看输出

成功上传后，在串口监视器中您将看到：

```
========================================
ESP32-C3 IMU Fusion Arduino项目启动
========================================
I2C总线初始化完成
检测到MPU6050设备，ID: 0x68
MPU6050初始化完成
Fusion AHRS算法初始化完成
系统准备就绪，开始数据采集...
========================================
姿态角: Roll=1.2°, Pitch=-0.8°, Yaw=45.6°
线性加速度: X=0.02g, Y=-0.01g, Z=0.00g
----------------------------------------
```

## 🔧 配置选项

### 采样频率调整
在`ESP32C3_IMU_Fusion.ino`中修改：
```cpp
#define SAMPLE_PERIOD_MS    10    // 10ms = 100Hz
```

### I2C引脚配置
在`ESP32C3_IMU_Fusion.ino`中修改：
```cpp
#define SDA_PIN 4    // I2C数据引脚
#define SCL_PIN 5    // I2C时钟引脚
```

### AHRS算法参数
在`initializeFusion()`函数中调整：
```cpp
const FusionAhrsSettings settings = {
    .convention = FusionConventionNwu,          // 坐标系
    .gain = 0.5f,                               // 融合增益
    .gyroscopeRange = 2000.0f,                  // 陀螺仪量程
    .accelerationRejection = 10.0f,             // 加速度拒绝阈值
    .magneticRejection = 10.0f,                 // 磁场拒绝阈值
    .recoveryTriggerPeriod = 500,               // 恢复触发周期
};
```

## 🐛 故障排除

### 编译错误

**问题**: "Fusion.h: No such file or directory"
**解决**: 确保Fusion库正确安装在Arduino库目录中

**问题**: "ESP32C3 Dev Module not found"
**解决**: 确保ESP32开发板包已正确安装

### 运行时错误

**问题**: "错误: IMU初始化失败!"
**解决**: 
1. 检查MPU6050连接是否正确
2. 确认I2C引脚配置 (SDA=GPIO4, SCL=GPIO5)
3. 检查传感器供电是否正常 (3.3V)
4. 尝试更换MPU6050模块

**问题**: "警告: 读取IMU数据失败"
**解决**:
1. 检查I2C连接稳定性
2. 降低I2C时钟频率: `Wire.setClock(100000);`
3. 检查传感器是否损坏

### 数据异常

**问题**: 姿态角数据跳动很大
**解决**:
1. 确保传感器静止校准
2. 调整AHRS算法增益参数
3. 检查采样频率设置
4. 避免传感器受到振动干扰

## 📚 API参考

### IMU_Driver类

```cpp
// 构造函数
IMU_Driver imu(0x68);

// 初始化
bool begin();

// 读取数据
bool readData(float& accel_x, float& accel_y, float& accel_z,
              float& gyro_x, float& gyro_y, float& gyro_z);

// 设置量程
bool setGyroRange(uint8_t range);   // GYRO_RANGE_250DPS, 500DPS, 1000DPS, 2000DPS
bool setAccelRange(uint8_t range);  // ACCEL_RANGE_2G, 4G, 8G, 16G
```

### Fusion库函数

```cpp
// 初始化AHRS
FusionAhrsInitialise(&ahrs);

// 设置参数
FusionAhrsSetSettings(&ahrs, &settings);

// 更新算法
FusionAhrsUpdateNoMagnetometer(&ahrs, gyroscope, accelerometer, deltaTime);

// 获取结果
FusionQuaternion quaternion = FusionAhrsGetQuaternion(&ahrs);
FusionEuler euler = FusionQuaternionToEuler(quaternion);
FusionVector linearAccel = FusionAhrsGetLinearAcceleration(&ahrs);
```

## 🎯 下一步开发

1. **添加磁力计支持**: 集成9轴IMU (如MPU9250)
2. **WiFi数据传输**: 通过WiFi发送姿态数据
3. **数据记录**: 添加SD卡数据记录功能
4. **实时可视化**: 开发PC端可视化工具
5. **多传感器融合**: 支持多个IMU传感器

## 📄 许可证

本项目基于MIT许可证开源。Fusion库版权归Seb Madgwick所有。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**祝您使用愉快！** 🎉
