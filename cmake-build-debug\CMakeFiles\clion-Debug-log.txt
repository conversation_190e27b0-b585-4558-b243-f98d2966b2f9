"D:\CLion 2024.3\CLion 2024.1.6\bin\cmake\win\x64\bin\cmake.exe" -DCMAKE_BUILD_TYPE=Debug "-DCMAKE_MAKE_PROGRAM=D:/CLion 2024.3/CLion 2024.1.6/bin/ninja/win/x64/ninja.exe" -G Ninja -S C:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\Arduino_ESP32C3_IMU -B C:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\Arduino_ESP32C3_IMU\cmake-build-debug
-- Configuring done (1.5s)
-- Generating done (0.0s)
-- Build files have been written to: C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/cmake-build-debug
