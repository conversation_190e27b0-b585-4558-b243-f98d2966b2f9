/**
 * @file imu_fusion_engine.h
 * @brief IMU数据融合引擎头文件 (纯C实现)
 * <AUTHOR> IMU Fusion Project
 * @version 2.0
 * @date 2025-01-02
 * 
 * 使用纯C封装Fusion库的AHRS算法
 * 提供简单易用的C函数API接口
 */

#ifndef IMU_FUSION_ENGINE_H
#define IMU_FUSION_ENGINE_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "Fusion.h"
#include "imu_sensor_interface.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 姿态数据结构
 */
typedef struct {
    float roll;         ///< 横滚角 (度)
    float pitch;        ///< 俯仰角 (度)
    float yaw;          ///< 偏航角 (度)
    float heading;      ///< 航向角 (度)
} attitude_data_t;

/**
 * @brief 四元数数据结构
 */
typedef struct {
    float w;            ///< 四元数实部
    float x;            ///< 四元数虚部i
    float y;            ///< 四元数虚部j
    float z;            ///< 四元数虚部k
} quaternion_data_t;

/**
 * @brief 融合算法配置结构
 */
typedef struct {
    FusionConvention convention;        ///< 坐标系约定 (默认: NWU)
    float gain;                         ///< 融合增益 (默认: 0.5)
    float gyroscope_range;              ///< 陀螺仪量程 (度/秒)
    float acceleration_rejection;       ///< 加速度拒绝阈值 (度)
    float magnetic_rejection;           ///< 磁场拒绝阈值 (度)
    uint32_t recovery_trigger_period;   ///< 恢复触发周期 (采样数)
    float sample_period;                ///< 采样周期 (秒)
    bool use_magnetometer;              ///< 是否使用磁力计
} fusion_config_t;

/**
 * @brief 融合引擎实例结构
 */
typedef struct {
    FusionAhrs ahrs;                    ///< Fusion AHRS算法实例
    fusion_config_t config;             ///< 融合算法配置
    
    bool initialized;                   ///< 初始化标志
    uint32_t sample_count;              ///< 采样计数
    uint64_t last_update_time;          ///< 上次更新时间 (微秒)
    
    // 数据缓存
    attitude_data_t attitude;           ///< 姿态数据
    quaternion_data_t quaternion;       ///< 四元数数据
    
    // 统计信息
    float max_accel_magnitude;          ///< 最大加速度幅值
    float max_gyro_magnitude;           ///< 最大角速度幅值
} imu_fusion_engine_t;

/**
 * @brief 创建融合引擎实例
 * @return 融合引擎实例指针，失败返回NULL
 */
imu_fusion_engine_t* imu_fusion_engine_create(void);

/**
 * @brief 销毁融合引擎实例
 * @param engine 融合引擎实例指针
 */
void imu_fusion_engine_destroy(imu_fusion_engine_t *engine);

/**
 * @brief 初始化融合引擎
 * @param engine 融合引擎实例指针
 * @param config 融合算法配置，NULL使用默认配置
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_fusion_engine_init(imu_fusion_engine_t *engine, const fusion_config_t *config);

/**
 * @brief 更新IMU数据
 * @param engine 融合引擎实例指针
 * @param data IMU传感器数据
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_fusion_engine_update(imu_fusion_engine_t *engine, const imu_data_t *data);

/**
 * @brief 更新IMU数据 (分离接口)
 * @param engine 融合引擎实例指针
 * @param accel_x 加速度计X轴 (g)
 * @param accel_y 加速度计Y轴 (g)
 * @param accel_z 加速度计Z轴 (g)
 * @param gyro_x 陀螺仪X轴 (度/秒)
 * @param gyro_y 陀螺仪Y轴 (度/秒)
 * @param gyro_z 陀螺仪Z轴 (度/秒)
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_fusion_engine_update_manual(imu_fusion_engine_t *engine,
                                         float accel_x, float accel_y, float accel_z,
                                         float gyro_x, float gyro_y, float gyro_z);

/**
 * @brief 获取四元数
 * @param engine 融合引擎实例指针
 * @param quat 输出的四元数数据
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_fusion_engine_get_quaternion(imu_fusion_engine_t *engine, quaternion_data_t *quat);

/**
 * @brief 获取姿态角
 * @param engine 融合引擎实例指针
 * @param attitude 输出的姿态数据
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_fusion_engine_get_attitude(imu_fusion_engine_t *engine, attitude_data_t *attitude);

/**
 * @brief 获取欧拉角 (度)
 * @param engine 融合引擎实例指针
 * @param roll 横滚角
 * @param pitch 俯仰角
 * @param yaw 偏航角
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_fusion_engine_get_euler_angles(imu_fusion_engine_t *engine, 
                                            float *roll, float *pitch, float *yaw);

/**
 * @brief 获取航向角 (度)
 * @param engine 融合引擎实例指针
 * @return 航向角，失败返回0.0f
 */
float imu_fusion_engine_get_heading(imu_fusion_engine_t *engine);

/**
 * @brief 重置融合算法
 * @param engine 融合引擎实例指针
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_fusion_engine_reset(imu_fusion_engine_t *engine);

/**
 * @brief 设置融合配置
 * @param engine 融合引擎实例指针
 * @param config 新的配置参数
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_fusion_engine_set_config(imu_fusion_engine_t *engine, const fusion_config_t *config);

/**
 * @brief 获取当前配置
 * @param engine 融合引擎实例指针
 * @param config 输出的配置参数
 * @return ESP_OK成功，其他值失败
 */
esp_err_t imu_fusion_engine_get_config(imu_fusion_engine_t *engine, fusion_config_t *config);

/**
 * @brief 获取采样计数
 * @param engine 融合引擎实例指针
 * @return 采样计数
 */
uint32_t imu_fusion_engine_get_sample_count(imu_fusion_engine_t *engine);

/**
 * @brief 获取统计信息
 * @param engine 融合引擎实例指针
 * @param max_accel 最大加速度幅值
 * @param max_gyro 最大角速度幅值
 */
void imu_fusion_engine_get_statistics(imu_fusion_engine_t *engine, float *max_accel, float *max_gyro);

/**
 * @brief 清除统计信息
 * @param engine 融合引擎实例指针
 */
void imu_fusion_engine_clear_statistics(imu_fusion_engine_t *engine);

/**
 * @brief 检查是否已初始化
 * @param engine 融合引擎实例指针
 * @return true已初始化，false未初始化
 */
bool imu_fusion_engine_is_initialized(imu_fusion_engine_t *engine);

/**
 * @brief 创建默认融合配置
 * @param sample_rate 采样率 (Hz)
 * @return 默认配置
 */
fusion_config_t imu_fusion_engine_create_default_config(float sample_rate);

/**
 * @brief 打印四元数到控制台 (兼容原有格式)
 * @param engine 融合引擎实例指针
 * @param precision 小数位数
 */
void imu_fusion_engine_print_quaternion(imu_fusion_engine_t *engine, int precision);

/**
 * @brief 打印欧拉角到控制台
 * @param engine 融合引擎实例指针
 * @param precision 小数位数
 */
void imu_fusion_engine_print_euler_angles(imu_fusion_engine_t *engine, int precision);

#ifdef __cplusplus
}
#endif

#endif // IMU_FUSION_ENGINE_H
