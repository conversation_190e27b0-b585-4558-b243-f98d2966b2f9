# ESP32-C3 IMU Fusion with SH5001

## 概述

本项目已成功从MPU6050迁移到SH5001六轴惯性测量单元。使用Fusion库进行姿态估计和传感器数据融合。

## 硬件连接

```
ESP32-C3    SH5001
--------    ------
GPIO8   --> SDA
GPIO9   --> SCL
3.3V    --> VDD
GND     --> GND
```

**重要**: SH5001的SDO引脚状态决定I2C地址：
- SDO接VDD: I2C地址 = 0x37 (默认)
- SDO接GND: I2C地址 = 0x36

## 主要修改

### 1. IMU_Driver.h
- 将MPU6050寄存器定义改为SH5001寄存器定义
- 更新I2C地址为0x37 (SH5001默认地址)
- 添加SH5001特有的配置参数
- 更新量程配置选项

### 2. IMU_Driver.cpp
- 修改初始化流程，使用SH5001的软复位和配置方法
- 更新数据读取方式，适配SH5001的小端格式
- 修改设备ID检查 (0xA1)
- 添加温度读取功能
- 实现SH5001特有的配置方法

### 3. ESP32C3_IMU_Fusion.ino
- 更新项目描述和注释
- 修改输出格式为四元数 (与原SH5001示例保持一致)
- 调整输出频率为100Hz

## 输出格式

程序输出四元数格式，与原SH5001示例保持一致：
```
w,x,y,z
```

例如：
```
1.0000,-0.0001,0.0002,-0.0003
0.9999,-0.0001,0.0003,-0.0002
```

## 配置参数

- 加速度计量程: ±16G
- 陀螺仪量程: ±2000°/s
- 采样频率: 125Hz
- 输出频率: 100Hz
- 滤波器: 启用

## 使用说明

1. 按照硬件连接图连接ESP32-C3和SH5001
2. 确认SH5001的SDO引脚连接状态
3. 如需修改I2C地址，在IMU_Driver.h中修改SH5001_ADDR定义
4. 编译并上传代码到ESP32-C3
5. 打开串口监视器 (波特率115200) 观察四元数输出

## 故障排除

如果遇到初始化失败：
1. 检查I2C接线 (SDA=GPIO8, SCL=GPIO9)
2. 检查电源连接 (VDD=3.3V, GND)
3. 确认I2C地址设置正确
4. 检查SH5001芯片是否损坏

## 兼容性

本驱动保持了与原MPU6050驱动相同的接口，因此Fusion库的其他部分无需修改。
