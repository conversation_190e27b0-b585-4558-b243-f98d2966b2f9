/**
 * @file Advanced_Usage.ino
 * @brief IMU融合库高级使用示例
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 这个示例展示了IMU融合库的高级功能：
 * - 手动传感器配置
 * - 传感器校准
 * - 多种数据输出格式
 * - 统计信息监控
 * - 错误处理
 */

#include <IMU_Fusion_Library.h>

// 创建IMU融合库实例
IMU_Fusion_Library imu_fusion;

// 配置参数
#define SAMPLE_RATE_HZ      100
#define UPDATE_INTERVAL_MS  10
#define PRINT_INTERVAL_MS   500     // 2Hz输出用于详细信息
#define STATS_INTERVAL_MS   10000   // 10秒统计信息

// I2C引脚定义
#define SDA_PIN 8
#define SCL_PIN 9

// 时间控制变量
unsigned long last_update_time = 0;
unsigned long last_print_time = 0;
unsigned long last_stats_time = 0;

// 输出模式
enum OutputMode {
    MODE_QUATERNION = 0,
    MODE_EULER_ANGLES,
    MODE_RAW_DATA,
    MODE_ALL_DATA
};

OutputMode current_mode = MODE_QUATERNION;
uint32_t mode_switch_counter = 0;

void setup() {
    Serial.begin(115200);
    while (!Serial) {
        delay(10);
    }
    
    Serial.println("========================================");
    Serial.println("IMU融合库高级使用示例");
    Serial.println("========================================");
    
    // 手动创建SH5001传感器实例
    SH5001_Sensor* sh5001 = new SH5001_Sensor(SH5001_ADDR);
    
    // 设置传感器到融合库
    imu_fusion.setSensor(sh5001, true); // 接管所有权
    
    // 创建自定义配置
    IMU_Config sensor_config;
    sensor_config.accel_range = 16;         // ±16g
    sensor_config.gyro_range = 2000;        // ±2000°/s
    sensor_config.sample_rate = 125;        // 125Hz
    sensor_config.enable_filter = true;     // 启用数字滤波器
    sensor_config.filter_bandwidth = 40;    // 40% ODR截止频率
    
    Fusion_Config fusion_config;
    fusion_config.convention = FusionConventionNwu;
    fusion_config.gain = 0.5f;
    fusion_config.gyroscope_range = 2000.0f;
    fusion_config.acceleration_rejection = 10.0f;
    fusion_config.magnetic_rejection = 10.0f;
    fusion_config.recovery_trigger_period = 500; // 5秒 @ 100Hz
    fusion_config.sample_period = 1.0f / SAMPLE_RATE_HZ;
    fusion_config.use_magnetometer = false;
    
    // 初始化I2C
    Wire.begin(SDA_PIN, SCL_PIN);
    Wire.setClock(400000);
    
    // 检查传感器连接
    if (!imu_fusion.isSensorConnected()) {
        Serial.println("错误: SH5001传感器未连接!");
        Serial.println("请检查硬件连接和I2C地址设置");
        while (1) {
            delay(1000);
        }
    }
    
    Serial.println("SH5001传感器连接正常");
    
    // 初始化融合库
    IMU_Status status = imu_fusion.begin(sensor_config, fusion_config);
    if (status != IMU_Status::OK) {
        Serial.printf("错误: 融合库初始化失败! 状态: %d\n", (int)status);
        Serial.printf("错误信息: %s\n", imu_fusion.getLastError());
        while (1) {
            delay(1000);
        }
    }
    
    // 显示详细信息
    Serial.println("库信息:");
    Serial.println(imu_fusion.getLibraryInfo());
    Serial.println();
    
    // 询问是否进行校准
    Serial.println("是否进行传感器校准? (y/n)");
    Serial.println("校准期间请将传感器放置在水平静止位置");
    
    // 等待用户输入 (简化处理，实际应用中可以使用更复杂的输入处理)
    delay(3000);
    
    // 自动进行校准 (在实际应用中可以根据用户输入决定)
    Serial.println("开始自动校准...");
    status = imu_fusion.calibrate(5000); // 5秒校准
    
    if (status == IMU_Status::OK) {
        Serial.println("校准完成!");
    } else {
        Serial.printf("校准失败: %s\n", imu_fusion.getLastError());
    }
    
    Serial.println();
    Serial.println("系统准备就绪!");
    Serial.println("输出模式将每30秒自动切换:");
    Serial.println("0: 四元数, 1: 欧拉角, 2: 原始数据, 3: 全部数据");
    Serial.println("========================================");
    
    last_update_time = millis();
    last_print_time = millis();
    last_stats_time = millis();
}

void loop() {
    unsigned long current_time = millis();
    
    // 更新IMU数据
    if (current_time - last_update_time >= UPDATE_INTERVAL_MS) {
        last_update_time = current_time;
        
        IMU_Status status = imu_fusion.update();
        if (status != IMU_Status::OK) {
            Serial.printf("数据更新失败: %s\n", imu_fusion.getLastError());
        }
    }
    
    // 输出数据
    if (current_time - last_print_time >= PRINT_INTERVAL_MS) {
        last_print_time = current_time;
        
        // 每30秒切换输出模式
        if ((current_time / 30000) != mode_switch_counter) {
            mode_switch_counter = current_time / 30000;
            current_mode = (OutputMode)((int)current_mode + 1);
            if (current_mode > MODE_ALL_DATA) {
                current_mode = MODE_QUATERNION;
            }
            Serial.printf("\n--- 切换到输出模式 %d ---\n", (int)current_mode);
        }
        
        // 根据当前模式输出数据
        switch (current_mode) {
            case MODE_QUATERNION:
                Serial.print("四元数: ");
                imu_fusion.printQuaternion(4);
                break;
                
            case MODE_EULER_ANGLES:
                Serial.print("欧拉角: ");
                imu_fusion.printEulerAngles(2);
                break;
                
            case MODE_RAW_DATA:
                Serial.print("原始数据: ");
                imu_fusion.printSensorData(3);
                break;
                
            case MODE_ALL_DATA:
                {
                    float w, x, y, z;
                    float roll, pitch, yaw;
                    IMU_Data raw_data;
                    
                    if (imu_fusion.getQuaternion(w, x, y, z) == IMU_Status::OK &&
                        imu_fusion.getEulerAngles(roll, pitch, yaw) == IMU_Status::OK &&
                        imu_fusion.getRawData(raw_data) == IMU_Status::OK) {
                        
                        Serial.printf("四元数: w=%.4f, x=%.4f, y=%.4f, z=%.4f\n", w, x, y, z);
                        Serial.printf("欧拉角: roll=%.2f°, pitch=%.2f°, yaw=%.2f°\n", roll, pitch, yaw);
                        Serial.printf("航向角: %.2f°\n", imu_fusion.getHeading());
                        Serial.printf("加速度: x=%.3fg, y=%.3fg, z=%.3fg\n", 
                                     raw_data.accel_x, raw_data.accel_y, raw_data.accel_z);
                        Serial.printf("角速度: x=%.3f°/s, y=%.3f°/s, z=%.3f°/s\n", 
                                     raw_data.gyro_x, raw_data.gyro_y, raw_data.gyro_z);
                        Serial.printf("温度: %.1f°C\n", raw_data.temperature);
                        Serial.println();
                    }
                }
                break;
        }
    }
    
    // 输出统计信息
    if (current_time - last_stats_time >= STATS_INTERVAL_MS) {
        last_stats_time = current_time;
        
        uint32_t total_samples, failed_samples, uptime;
        float success_rate;
        imu_fusion.getStatistics(total_samples, failed_samples, success_rate, uptime);
        
        Serial.println("\n--- 统计信息 ---");
        Serial.printf("运行时间: %lu ms\n", uptime);
        Serial.printf("总采样数: %lu\n", total_samples);
        Serial.printf("失败采样数: %lu\n", failed_samples);
        Serial.printf("成功率: %.2f%%\n", success_rate);
        Serial.printf("平均采样率: %.1f Hz\n", (float)total_samples / (uptime / 1000.0f));
        Serial.println("--- 统计信息结束 ---\n");
    }
    
    delay(1);
}
