# MPU6050 到 SH5001 迁移总结

## 主要差异对比

### 1. I2C地址
| 传感器 | I2C地址 | 备注 |
|--------|---------|------|
| MPU6050 | 0x68 | 固定地址 |
| SH5001 | 0x37/0x36 | 由SDO引脚决定 |

### 2. 设备ID
| 传感器 | 设备ID | 寄存器地址 |
|--------|--------|-----------|
| MPU6050 | 0x68 | 0x75 (WHO_AM_I) |
| SH5001 | 0xA1 | 0x1F (CHIP_ID) |

### 3. 数据格式
| 传感器 | 数据格式 | 字节顺序 |
|--------|----------|----------|
| MPU6050 | 大端 | 高字节在前 |
| SH5001 | 小端 | 低字节在前 |

### 4. 寄存器布局
| 数据类型 | MPU6050 | SH5001 |
|----------|---------|--------|
| 加速度计X | 0x3B-0x3C | 0x00-0x01 |
| 加速度计Y | 0x3D-0x3E | 0x02-0x03 |
| 加速度计Z | 0x3F-0x40 | 0x04-0x05 |
| 陀螺仪X | 0x43-0x44 | 0x06-0x07 |
| 陀螺仪Y | 0x45-0x46 | 0x08-0x09 |
| 陀螺仪Z | 0x47-0x48 | 0x0A-0x0B |

### 5. 初始化流程
| 步骤 | MPU6050 | SH5001 |
|------|---------|--------|
| 复位 | 写PWR_MGMT_1寄存器 | 软复位序列 |
| 唤醒 | 清除睡眠位 | 无需额外唤醒 |
| 配置 | 直接写配置寄存器 | 读-修改-写配置寄存器 |

### 6. 量程配置
| 传感器 | MPU6050支持的量程 | SH5001支持的量程 |
|--------|-------------------|-------------------|
| 加速度计 | ±2g, ±4g, ±8g, ±16g | ±4g, ±8g, ±16g |
| 陀螺仪 | ±250°/s, ±500°/s, ±1000°/s, ±2000°/s | ±31°/s, ±62°/s, ±125°/s, ±250°/s, ±500°/s, ±1000°/s, ±2000°/s, ±4000°/s |

## 代码修改要点

### 1. 数据读取修改
```cpp
// MPU6050 (大端格式)
accel_raw[0] = combineBytes(buffer[0], buffer[1]);

// SH5001 (小端格式)
accel_raw[0] = (int16_t)((buffer[1] << 8) | buffer[0]);
```

### 2. 配置方法修改
```cpp
// MPU6050 - 直接写入
writeRegister(MPU6050_GYRO_CONFIG, range);

// SH5001 - 读-修改-写
reg_data = readRegister(SH5001_GYRO_CONF1);
reg_data = (reg_data & 0x8F) | range;
writeRegister(SH5001_GYRO_CONF1, reg_data);
```

### 3. 输出格式修改
```cpp
// 原来输出欧拉角和线性加速度
Serial.print("姿态角: Roll="); Serial.print(euler.angle.roll, 1);

// 现在输出四元数
Serial.print(quaternion.element.w, 4); Serial.print(",");
```

## 保持不变的部分

1. **Fusion库接口**: 完全兼容，无需修改
2. **主程序逻辑**: 数据处理流程保持一致
3. **I2C通信**: 使用相同的Wire库
4. **采样频率**: 保持100Hz采样
5. **数据单位**: 加速度计(g)，陀螺仪(°/s)

## 验证要点

1. ✅ 设备ID正确读取 (0xA1)
2. ✅ 数据格式正确转换 (小端)
3. ✅ 输出格式与原SH5001示例一致
4. ✅ 保持原有接口兼容性
5. ✅ 配置参数正确设置

## 性能对比

| 特性 | MPU6050 | SH5001 | 备注 |
|------|---------|--------|------|
| 功耗 | 中等 | 较低 | SH5001更省电 |
| 精度 | 16位 | 16位 | 相同精度 |
| 量程选择 | 有限 | 更多选择 | SH5001选择更丰富 |
| 温度传感器 | 有 | 有 | 都支持温度读取 |
| 中断功能 | 基础 | 丰富 | SH5001中断功能更强 |
