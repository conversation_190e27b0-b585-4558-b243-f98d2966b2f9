# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.30

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Arduino_ESP32C3_IMU
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/build/
# =============================================================================
# Object build statements for EXECUTABLE target Arduino_ESP32C3_IMU


#############################################
# Order-only phony target for Arduino_ESP32C3_IMU

build cmake_object_order_depends_target_Arduino_ESP32C3_IMU: phony || .

build CMakeFiles/Arduino_ESP32C3_IMU.dir/ESP32C3_IMU_Fusion/IMU_Driver.cpp.obj: CXX_COMPILER__Arduino_ESP32C3_IMU_unscanned_ C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion/IMU_Driver.cpp || cmake_object_order_depends_target_Arduino_ESP32C3_IMU
  DEP_FILE = CMakeFiles\Arduino_ESP32C3_IMU.dir\ESP32C3_IMU_Fusion\IMU_Driver.cpp.obj.d
  FLAGS = -std=gnu++14
  INCLUDES = -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src
  OBJECT_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir
  OBJECT_FILE_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir\ESP32C3_IMU_Fusion
  TARGET_COMPILE_PDB = CMakeFiles\Arduino_ESP32C3_IMU.dir\
  TARGET_PDB = Arduino_ESP32C3_IMU.pdb

build CMakeFiles/Arduino_ESP32C3_IMU.dir/ESP32C3_IMU_Fusion/main.c.obj: C_COMPILER__Arduino_ESP32C3_IMU_unscanned_ C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion/main.c || cmake_object_order_depends_target_Arduino_ESP32C3_IMU
  DEP_FILE = CMakeFiles\Arduino_ESP32C3_IMU.dir\ESP32C3_IMU_Fusion\main.c.obj.d
  INCLUDES = -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src
  OBJECT_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir
  OBJECT_FILE_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir\ESP32C3_IMU_Fusion
  TARGET_COMPILE_PDB = CMakeFiles\Arduino_ESP32C3_IMU.dir\
  TARGET_PDB = Arduino_ESP32C3_IMU.pdb

build CMakeFiles/Arduino_ESP32C3_IMU.dir/libraries/Fusion/src/FusionAhrs.c.obj: C_COMPILER__Arduino_ESP32C3_IMU_unscanned_ C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src/FusionAhrs.c || cmake_object_order_depends_target_Arduino_ESP32C3_IMU
  DEP_FILE = CMakeFiles\Arduino_ESP32C3_IMU.dir\libraries\Fusion\src\FusionAhrs.c.obj.d
  INCLUDES = -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src
  OBJECT_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir
  OBJECT_FILE_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir\libraries\Fusion\src
  TARGET_COMPILE_PDB = CMakeFiles\Arduino_ESP32C3_IMU.dir\
  TARGET_PDB = Arduino_ESP32C3_IMU.pdb

build CMakeFiles/Arduino_ESP32C3_IMU.dir/libraries/Fusion/src/FusionCompass.c.obj: C_COMPILER__Arduino_ESP32C3_IMU_unscanned_ C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src/FusionCompass.c || cmake_object_order_depends_target_Arduino_ESP32C3_IMU
  DEP_FILE = CMakeFiles\Arduino_ESP32C3_IMU.dir\libraries\Fusion\src\FusionCompass.c.obj.d
  INCLUDES = -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src
  OBJECT_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir
  OBJECT_FILE_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir\libraries\Fusion\src
  TARGET_COMPILE_PDB = CMakeFiles\Arduino_ESP32C3_IMU.dir\
  TARGET_PDB = Arduino_ESP32C3_IMU.pdb

build CMakeFiles/Arduino_ESP32C3_IMU.dir/libraries/Fusion/src/FusionOffset.c.obj: C_COMPILER__Arduino_ESP32C3_IMU_unscanned_ C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src/FusionOffset.c || cmake_object_order_depends_target_Arduino_ESP32C3_IMU
  DEP_FILE = CMakeFiles\Arduino_ESP32C3_IMU.dir\libraries\Fusion\src\FusionOffset.c.obj.d
  INCLUDES = -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/ESP32C3_IMU_Fusion -IC:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/libraries/Fusion/src
  OBJECT_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir
  OBJECT_FILE_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir\libraries\Fusion\src
  TARGET_COMPILE_PDB = CMakeFiles\Arduino_ESP32C3_IMU.dir\
  TARGET_PDB = Arduino_ESP32C3_IMU.pdb


# =============================================================================
# Link build statements for EXECUTABLE target Arduino_ESP32C3_IMU


#############################################
# Link the executable Arduino_ESP32C3_IMU.exe

build Arduino_ESP32C3_IMU.exe: CXX_EXECUTABLE_LINKER__Arduino_ESP32C3_IMU_ CMakeFiles/Arduino_ESP32C3_IMU.dir/ESP32C3_IMU_Fusion/IMU_Driver.cpp.obj CMakeFiles/Arduino_ESP32C3_IMU.dir/ESP32C3_IMU_Fusion/main.c.obj CMakeFiles/Arduino_ESP32C3_IMU.dir/libraries/Fusion/src/FusionAhrs.c.obj CMakeFiles/Arduino_ESP32C3_IMU.dir/libraries/Fusion/src/FusionCompass.c.obj CMakeFiles/Arduino_ESP32C3_IMU.dir/libraries/Fusion/src/FusionOffset.c.obj
  LINK_LIBRARIES = -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = CMakeFiles\Arduino_ESP32C3_IMU.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\Arduino_ESP32C3_IMU.dir\
  TARGET_FILE = Arduino_ESP32C3_IMU.exe
  TARGET_IMPLIB = libArduino_ESP32C3_IMU.dll.a
  TARGET_PDB = Arduino_ESP32C3_IMU.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\Arduino_ESP32C3_IMU\build && D:\esp\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\Arduino_ESP32C3_IMU -BC:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\Arduino_ESP32C3_IMU\build"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\Arduino_ESP32C3_IMU\build && D:\esp\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\Arduino_ESP32C3_IMU -BC:\Users\<USER>\Desktop\IMU\SH5001_ESP32C3_Example\Arduino_ESP32C3_IMU\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build Arduino_ESP32C3_IMU: phony Arduino_ESP32C3_IMU.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/build

build all: phony Arduino_ESP32C3_IMU.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/CMakeLists.txt CMakeCache.txt CMakeFiles/3.30.2/CMakeCCompiler.cmake CMakeFiles/3.30.2/CMakeCXXCompiler.cmake CMakeFiles/3.30.2/CMakeRCCompiler.cmake CMakeFiles/3.30.2/CMakeSystem.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCCompiler.cmake.in D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCCompilerABI.c D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCInformation.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXInformation.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCompilerIdDetection.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerSupport.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineRCCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeFindBinUtils.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeGenericSystem.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeNinjaFindMake.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeParseImplicitLinkInfo.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeParseLibraryArchitecture.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeRCCompiler.cmake.in D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeRCInformation.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeSystem.cmake.in D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestRCCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/CrayClang-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-C.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-FindBinUtils.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/LCC-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/OrangeC-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/TI-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/TIClang-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Tasking-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/FeatureTesting.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-Determine-CXX.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-GNU-C-ABI.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-GNU-C.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX-ABI.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-GNU.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-windres.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/Desktop/IMU/SH5001_ESP32C3_Example/Arduino_ESP32C3_IMU/CMakeLists.txt CMakeCache.txt CMakeFiles/3.30.2/CMakeCCompiler.cmake CMakeFiles/3.30.2/CMakeCXXCompiler.cmake CMakeFiles/3.30.2/CMakeRCCompiler.cmake CMakeFiles/3.30.2/CMakeSystem.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCCompiler.cmake.in D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCCompilerABI.c D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCInformation.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXInformation.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCompilerIdDetection.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerSupport.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineRCCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeFindBinUtils.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeGenericSystem.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeNinjaFindMake.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeParseImplicitLinkInfo.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeParseLibraryArchitecture.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeRCCompiler.cmake.in D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeRCInformation.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeSystem.cmake.in D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestRCCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/CrayClang-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-C.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-FindBinUtils.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/LCC-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/OrangeC-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/TI-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/TIClang-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Tasking-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/FeatureTesting.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-Determine-CXX.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-GNU-C-ABI.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-GNU-C.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX-ABI.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-GNU.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows-windres.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Windows.cmake D$:/esp/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
