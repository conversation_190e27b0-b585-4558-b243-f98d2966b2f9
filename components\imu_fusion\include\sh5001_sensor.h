/**
 * @file sh5001_sensor.h
 * @brief SH5001 IMU传感器驱动头文件 (纯C实现)
 * <AUTHOR> IMU Fusion Project
 * @version 2.0
 * @date 2025-01-02
 * 
 * 使用纯C和ESP-IDF I2C驱动实现SH5001传感器驱动
 */

#ifndef SH5001_SENSOR_H
#define SH5001_SENSOR_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "driver/i2c.h"
#include "imu_sensor_interface.h"

#ifdef __cplusplus
extern "C" {
#endif

// SH5001寄存器地址定义
#define SH5001_ADDR             0x37    // SH5001 I2C地址 (SDO=VDD)
#define SH5001_ADDR_ALT         0x36    // SH5001 I2C地址 (SDO=GND)
#define SH5001_CHIP_ID          0x1F    // 设备ID寄存器
#define SH5001_EXPECTED_ID      0xA1    // 期望的设备ID

// 数据寄存器
#define SH5001_ACC_XL           0x00    // 加速度计X轴低字节
#define SH5001_ACC_XH           0x01    // 加速度计X轴高字节
#define SH5001_ACC_YL           0x02    // 加速度计Y轴低字节
#define SH5001_ACC_YH           0x03    // 加速度计Y轴高字节
#define SH5001_ACC_ZL           0x04    // 加速度计Z轴低字节
#define SH5001_ACC_ZH           0x05    // 加速度计Z轴高字节
#define SH5001_GYRO_XL          0x06    // 陀螺仪X轴低字节
#define SH5001_GYRO_XH          0x07    // 陀螺仪X轴高字节
#define SH5001_GYRO_YL          0x08    // 陀螺仪Y轴低字节
#define SH5001_GYRO_YH          0x09    // 陀螺仪Y轴高字节
#define SH5001_GYRO_ZL          0x0A    // 陀螺仪Z轴低字节
#define SH5001_GYRO_ZH          0x0B    // 陀螺仪Z轴高字节
#define SH5001_TEMP_ZL          0x0C    // 温度低字节
#define SH5001_TEMP_ZH          0x0D    // 温度高字节

// 配置寄存器
#define SH5001_ACC_CONF0        0x20    // 加速度计配置寄存器0
#define SH5001_ACC_CONF1        0x21    // 加速度计配置寄存器1
#define SH5001_ACC_CONF2        0x22    // 加速度计配置寄存器2
#define SH5001_GYRO_CONF0       0x23    // 陀螺仪配置寄存器0
#define SH5001_GYRO_CONF1       0x24    // 陀螺仪配置寄存器1
#define SH5001_GYRO_CONF2       0x25    // 陀螺仪配置寄存器2
#define SH5001_TEMP_CONF0       0x28    // 温度配置寄存器0
#define SH5001_POWER_MODE       0x30    // 电源模式寄存器

// 量程配置值
#define SH5001_ACCEL_RANGE_4G   0x10    // ±4g
#define SH5001_ACCEL_RANGE_8G   0x20    // ±8g
#define SH5001_ACCEL_RANGE_16G  0x30    // ±16g

#define SH5001_GYRO_RANGE_250   0x30    // ±250°/s
#define SH5001_GYRO_RANGE_500   0x40    // ±500°/s
#define SH5001_GYRO_RANGE_1000  0x50    // ±1000°/s
#define SH5001_GYRO_RANGE_2000  0x60    // ±2000°/s

// 输出数据率配置
#define SH5001_ODR_125HZ        0x03    // 125Hz
#define SH5001_ODR_250HZ        0x04    // 250Hz
#define SH5001_ODR_500HZ        0x05    // 500Hz
#define SH5001_ODR_1000HZ       0x06    // 1000Hz

/**
 * @brief SH5001传感器私有数据结构
 */
typedef struct {
    i2c_port_t i2c_port;               ///< I2C端口号
    uint8_t device_address;             ///< I2C设备地址
    float gyro_scale;                   ///< 陀螺仪量程转换因子
    float accel_scale;                  ///< 加速度计量程转换因子
    bool connection_verified;           ///< 连接验证标志
    
    // 校准偏移量
    float accel_offset[3];              ///< 加速度计偏移量
    float gyro_offset[3];               ///< 陀螺仪偏移量
} sh5001_priv_data_t;

/**
 * @brief SH5001传感器配置结构
 */
typedef struct {
    i2c_port_t i2c_port;               ///< I2C端口号
    int sda_pin;                        ///< SDA引脚
    int scl_pin;                        ///< SCL引脚
    uint32_t i2c_freq;                  ///< I2C时钟频率
    uint8_t device_address;             ///< 设备地址
} sh5001_init_config_t;

/**
 * @brief 创建SH5001传感器实例
 * @param init_config 初始化配置
 * @return 传感器实例指针，失败返回NULL
 */
imu_sensor_t* sh5001_create(const sh5001_init_config_t *init_config);

/**
 * @brief 获取SH5001传感器操作函数指针
 * @return 操作函数指针结构
 */
const imu_sensor_ops_t* sh5001_get_ops(void);

/**
 * @brief 创建默认SH5001初始化配置
 * @param i2c_port I2C端口号
 * @param sda_pin SDA引脚
 * @param scl_pin SCL引脚
 * @return 默认配置
 */
sh5001_init_config_t sh5001_create_default_init_config(i2c_port_t i2c_port, int sda_pin, int scl_pin);

/**
 * @brief 自动检测SH5001传感器
 * @param i2c_port I2C端口号
 * @param sda_pin SDA引脚
 * @param scl_pin SCL引脚
 * @param i2c_freq I2C频率
 * @return 检测到的传感器实例，失败返回NULL
 */
imu_sensor_t* sh5001_auto_detect(i2c_port_t i2c_port, int sda_pin, int scl_pin, uint32_t i2c_freq);

/**
 * @brief 获取SH5001温度数据
 * @param sensor 传感器实例指针
 * @return 温度值 (摄氏度)，失败返回0.0f
 */
float sh5001_get_temperature(imu_sensor_t *sensor);

/**
 * @brief 执行SH5001自检
 * @param sensor 传感器实例指针
 * @return ESP_OK成功，其他值失败
 */
esp_err_t sh5001_self_test(imu_sensor_t *sensor);

/**
 * @brief 获取SH5001校准偏移量
 * @param sensor 传感器实例指针
 * @param accel_offsets 加速度计偏移量 [x, y, z]
 * @param gyro_offsets 陀螺仪偏移量 [x, y, z]
 * @return ESP_OK成功，其他值失败
 */
esp_err_t sh5001_get_calibration_offsets(imu_sensor_t *sensor, 
                                        float accel_offsets[3], float gyro_offsets[3]);

/**
 * @brief 设置SH5001校准偏移量
 * @param sensor 传感器实例指针
 * @param accel_offsets 加速度计偏移量 [x, y, z]
 * @param gyro_offsets 陀螺仪偏移量 [x, y, z]
 * @return ESP_OK成功，其他值失败
 */
esp_err_t sh5001_set_calibration_offsets(imu_sensor_t *sensor, 
                                        const float accel_offsets[3], const float gyro_offsets[3]);

#ifdef __cplusplus
}
#endif

#endif // SH5001_SENSOR_H
